package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.AppButtonInput
import com.cleevio.fundedmind.application.module.highlight.command.UpdateHighlightCommand
import com.cleevio.fundedmind.domain.common.AppButton
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.domain.highlight.exception.HighlightButtonWithoutLinkException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class UpdateHighlightCommandHandlerTest(
    @Autowired private val underTest: UpdateHighlightCommandHandler,
    @Autowired private val highlightRepository: HighlightRepository,
) : IntegrationTest() {

    @Test
    fun `should update existing highlight`() {
        dataHelper.getHighlight(id = 1.toUUID())

        underTest.handle(
            defaultCommand(
                highlightId = 1.toUUID(),
                title = "New Title",
                description = "New description",
                visibleToTiers = listOf(StudentTier.EXCLUSIVE),
                visibleToDiscordUsers = true,
                linkUrl = "link",
                button = AppButtonInput(
                    text = "Button",
                    color = Color.GREEN,
                ),
            ),
        )

        val highlight = highlightRepository.findByIdOrNull(1.toUUID())!!

        highlight.run {
            title shouldBe "New Title"
            description shouldBe "New description"
            visibleToTiers shouldBe listOf(StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe "link"
            button!!.run {
                text shouldBe "Button"
                color shouldBe Color.GREEN
            }
        }
    }

    @Test
    fun `should remove link, button, title and description`() {
        dataHelper.getHighlight(
            id = 1.toUUID(),
            title = "Title",
            description = "Description",
            linkUrl = "link",
            button = AppButton("Button", Color.GREEN),
        )

        underTest.handle(
            defaultCommand(
                highlightId = 1.toUUID(),
                title = null,
                description = null,
                linkUrl = null,
                button = null,
            ),
        )

        val highlight = highlightRepository.findByIdOrNull(1.toUUID())!!

        highlight.run {
            title shouldBe null
            description shouldBe null
            linkUrl shouldBe null
            button shouldBe null
        }
    }

    @Test
    fun `should throw when update highlight with button and without link`() {
        dataHelper.getHighlight(id = 1.toUUID())

        shouldThrow<HighlightButtonWithoutLinkException> {
            underTest.handle(
                defaultCommand(
                    highlightId = 1.toUUID(),
                    linkUrl = null,
                    button = AppButtonInput(
                        text = "Button",
                        color = Color.BLUE,
                    ),
                ),
            )
        }
    }

    private fun defaultCommand(
        highlightId: UUID,
        title: String? = "New title",
        description: String? = "New description",
        visibleToTiers: List<StudentTier> = listOf(StudentTier.EXCLUSIVE),
        visibleToDiscordUsers: Boolean = true,
        linkUrl: String?,
        button: AppButtonInput?,
    ) = UpdateHighlightCommand(
        highlightId = highlightId,
        title = title,
        description = description,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        linkUrl = linkUrl,
        button = button,
    )
}
