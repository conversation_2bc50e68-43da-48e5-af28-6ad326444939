package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.highlight.command.DeleteHighlightCommand
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DeleteHighlightCommandHandlerTest(
    @Autowired private val underTest: DeleteHighlightCommandHandler,
    @Autowired private val highlightRepository: HighlightRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete highlight`() {
        dataHelper.getHighlight(id = 1.toUUID())

        underTest.handle(
            DeleteHighlightCommand(
                highlightId = 1.toUUID(),
            ),
        )

        highlightRepository.findByIdOrNull(1.toUUID())!!.isDeleted shouldBe true
    }
}
