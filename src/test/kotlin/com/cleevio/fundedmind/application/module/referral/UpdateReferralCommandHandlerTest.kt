package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.command.UpdateReferralCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class UpdateReferralCommandHandlerTest(
    @Autowired private val underTest: UpdateReferralCommandHandler,
    @Autowired private val referralRepository: ReferralRepository,
) : IntegrationTest() {

    @Test
    fun `should update referral with provided parameters`() {
        // given
        // Create a referral first
        val createdReferral = dataHelper.getReferral(
            title = "Original Title",
            description = "Original Description",
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = false,
            linkUrl = "https://original.com",
            rewardCouponCode = "ORIGINAL123",
        )

        // Create update command
        val updateCommand = UpdateReferralCommand(
            referralId = createdReferral.id,
            title = "Updated Title",
            description = "Updated Description",
            visibleToTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            linkUrl = "https://updated.com",
            rewardCouponCode = "UPDATED456",
        )

        // when
        underTest.handle(updateCommand)

        // then
        referralRepository.getReferenceById(createdReferral.id).run {
            title shouldBe "Updated Title"
            description shouldBe "Updated Description"
            visibleToTiers shouldBe listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe "https://updated.com"
            rewardCouponCode shouldBe "UPDATED456"
            // These properties should not be changed by the update
            listingOrder shouldBe 1
            published shouldBe false
        }
    }

    @Test
    fun `should update referral with null optional fields`() {
        // given
        // Create a referral first with all fields populated
        val createdReferral = dataHelper.getReferral(
            title = "Original Title",
            description = "Original Description",
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = false,
            linkUrl = "https://original.com",
            rewardCouponCode = "ORIGINAL123",
        )

        // Create update command with null optional fields
        val updateCommand = UpdateReferralCommand(
            referralId = createdReferral.id,
            title = null,
            description = null,
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            linkUrl = null,
            rewardCouponCode = null,
        )

        // when
        underTest.handle(updateCommand)

        // then
        referralRepository.getReferenceById(createdReferral.id).run {
            title shouldBe null
            description shouldBe null
            visibleToTiers shouldBe listOf(StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe null
            rewardCouponCode shouldBe null
        }
    }

    @Test
    fun `should update only specified fields`() {
        // given
        // Create a referral first
        val createdReferral = dataHelper.getReferral(
            title = "Original Title",
            description = "Original Description",
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = false,
            linkUrl = "https://original.com",
            rewardCouponCode = "ORIGINAL123",
        )

        // Create update command with only some fields changed
        val updateCommand = UpdateReferralCommand(
            referralId = createdReferral.id,
            title = "Updated Title",
            description = "Original Description", // Same as original
            visibleToTiers = listOf(StudentTier.BASECAMP), // Same as original
            visibleToDiscordUsers = false, // Same as original
            linkUrl = "https://original.com", // Same as original
            rewardCouponCode = "UPDATED456", // Changed
        )

        // when
        underTest.handle(updateCommand)

        // then
        referralRepository.getReferenceById(createdReferral.id).run {
            title shouldBe "Updated Title" // Changed
            description shouldBe "Original Description" // Unchanged
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP) // Unchanged
            visibleToDiscordUsers shouldBe false // Unchanged
            linkUrl shouldBe "https://original.com" // Unchanged
            rewardCouponCode shouldBe "UPDATED456" // Changed
        }
    }
}
