package com.cleevio.fundedmind.application.module.meeting.finder

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

class MeetingFinderServiceTest(
    @Autowired private val underTest: MeetingFinderService,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        // Meeting fully within the range
        "true,  15.04.2025 10:00, 15.04.2025 12:00",
        // Meeting starts within the range but finishes outside
        "true,  20.04.2025 10:00, 01.05.2025 10:00",
        // Meeting finishes within the range but starts outside
        "true,  30.03.2025 10:00, 15.04.2025 10:00",
        // Meeting completely outside the range (meeting before the range)
        "false, 01.12.2024 10:00, 31.12.2024 12:00",
        // Meeting completely outside the range (meeting after the range)
        "false, 01.12.2025 10:00, 31.12.2025 12:00",
    )
    fun `should find a meeting in range`(
        expectedResult: Boolean,
        meetingStart: String,
        meetingFinish: String,
    ) {
        // Given
        val rangeStart = "01.04.2025 00:00".toInstant("dd.MM.yyyy HH:mm")
        val rangeEnd = "30.04.2025 23:59".toInstant("dd.MM.yyyy HH:mm")

        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = meetingStart.toInstant(),
            finishAt = meetingFinish.toInstant(),
        )

        // When
        val result = underTest.findNonDeletedMeetingsInRange(rangeStart, rangeEnd)

        // Then
        result.isNotEmpty() shouldBe expectedResult
    }

    private fun String.toInstant(format: String = "dd.MM.yyyy HH:mm"): Instant =
        Instant.from(DateTimeFormatter.ofPattern(format).withZone(ZoneOffset.UTC).parse(this))
}
