package com.cleevio.fundedmind.application.module.gamepayout.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import io.swagger.v3.oas.annotations.media.Schema
import org.hibernate.validator.constraints.Range
import java.math.BigDecimal
import java.time.Instant
import java.time.Year
import java.util.UUID

data class GamePayoutOverviewQuery(
    val userId: UUID,
    val year: Year = Year.now(),
    @field:Range(min = 1, max = 100) val limit: Int = 10,
) : Query<GamePayoutOverviewQuery.Result> {

    @Schema(name = "GamePayoutOverviewResult")
    data class Result(
        val currentTotalPayout: BigDecimal,
        val currentPayoutGoal: BigDecimal,
        val year: Int,
        val latestStudentPayouts: List<LatestStudentPayout>,
    )

    @Schema(name = "LatestStudentPayout")
    data class LatestStudentPayout(
        val studentId: UUID,
        val firstName: String,
        val gameLevel: GameLevel?,
        val profilePicture: ImageResult?,
        val approvedAt: Instant,
        val payoutAmount: BigDecimal,
        val networkingVisibility: NetworkingVisibility,
    )
}

data class StudentGetsGameProgressOverviewQuery(
    val studentId: UUID,
) : Query<StudentGetsGameProgressOverviewQuery.Result> {

    @Schema(name = "StudentGetsGameProgressOverviewResult")
    data class Result(
        val gameLevel: GameLevel,
        val overallPayoutAmount: BigDecimal,
        val levelTwoProgress: LevelTwoProgress?,
        val levelThreeProgress: LevelThreeProgress?,
        val levelFourProgress: LevelFourProgress?,
        val levelFiveProgress: LevelWithPayoutProgress?,
        val levelSixProgress: LevelWithPayoutProgress?,
        val levelSevenProgress: LevelWithPayoutProgress?,
        val levelEightProgress: LevelWithPayoutProgress?,
        val levelNineProgress: LevelWithPayoutProgress?,
    )

    @Schema(name = "GameProgressLevelTwoProgress")
    data class LevelTwoProgress(
        val backtestingState: GameDocumentApprovalState?,
        val strategyModuleFinished: Boolean,
    )

    @Schema(name = "GameProgressLevelThreeProgress")
    data class LevelThreeProgress(
        val certificateState: GameDocumentApprovalState,
    )

    @Schema(name = "GameProgressLevelFourProgress")
    data class LevelFourProgress(
        val firstPayoutState: GameDocumentApprovalState,
    )

    @Schema(name = "GameProgressLevelWithPayoutProgress")
    data class LevelWithPayoutProgress(
        @Schema(description = "Compute progress bar e.g. 3200 / 5000 (payoutProgress / payoutGoal)")
        val payoutProgressInLevel: BigDecimal,
        val payoutGoal: BigDecimal,
    )
}

data class GetGameLeaderboardQuery(
    val userId: UUID,
    val year: Year = Year.now(),
) : Query<GetGameLeaderboardQuery.Result> {

    @Schema(name = "GetGameLeaderboardResult")
    data class Result(
        val studentPlayer: LeaderboardPlayer?,
        val topPlayers: List<LeaderboardPlayer>,
    )

    @Schema(name = "GetGameLeaderboardPlayer")
    data class LeaderboardPlayer(
        val position: Int?,
        val studentId: UUID,
        val firstName: String,
        val lastName: String,
        val city: String?,
        val profilePicture: ImageResult?,
        val gameLevel: GameLevel?,
        val gamePayout: BigDecimal?,
        val networkingVisibility: NetworkingVisibility,
    )
}

data class AdminGetsPayoutOverviewQuery(
    val year: Year = Year.now(),
) : Query<AdminGetsPayoutOverviewQuery.Result> {

    @Schema(name = "AdminGetsPayoutOverviewResult")
    data class Result(
        val offsetTotalPayout: BigDecimal,
        val realTotalPayout: BigDecimal,
        val approvedPayouts: Int,
        val waitingDocumentsCount: Int,
        val year: Int,
    )
}
