package com.cleevio.fundedmind.application.module.summerbootcamp

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.summerbootcamp.command.UpdateSummerBootcampRemainingSpotCommand
import com.cleevio.fundedmind.application.module.summerbootcamp.port.out.SummerBootcampPort
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.ZonedDateTime

class UpdateSummerBootcampRemainingSpotCommandHandlerTest(
    @Autowired private val underTest: UpdateSummerBootcampRemainingSpotCommandHandler,
    @Autowired private val summerBootcampPortPort: SummerBootcampPort,
) : IntegrationTest() {

    @Test
    fun `should update summer bootcamp remaining spots`() {
        // when
        underTest.handle(UpdateSummerBootcampRemainingSpotCommand(newValue = 42))

        // then
        summerBootcampPortPort.getRemainingSpots() shouldBe 42

        summerBootcampPortPort.getTimeline().run {
            startDate shouldBe ZonedDateTime.parse("2025-06-16T00:00:01+02:00[Europe/Prague]")
            endDate shouldBe ZonedDateTime.parse("2025-07-01T18:00:00+02:00[Europe/Prague]")
            discordTrialEndDate shouldBe ZonedDateTime.parse("2025-08-10T23:59:59+02:00[Europe/Prague]")
        }
    }
}
