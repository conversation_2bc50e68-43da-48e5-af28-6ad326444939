package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.command.UserRevertsFinishLessonCommand
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.domain.progress.CourseModuleProgressRepository
import com.cleevio.fundedmind.domain.progress.CourseProgressRepository
import com.cleevio.fundedmind.domain.progress.LessonProgressRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class UserRevertsFinishLessonCommandHandlerTest(
    @Autowired private val underTest: UserRevertsFinishLessonCommandHandler,
    @Autowired private val lessonProgressRepository: LessonProgressRepository,
    @Autowired private val courseModuleProgressRepository: CourseModuleProgressRepository,
    @Autowired private val courseProgressRepository: CourseProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should revert finished lesson progress`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        // 1 course, 1 module, 2 lessons - one lesson is finished and one is not
        dataHelper.getCourse(
            id = 0.toUUID(),
            traderId = dataHelper.getTrader(id = 0.toUUID()).id,
        ).also { course ->
            dataHelper.getCourseModule(
                id = 1.toUUID(),
                courseId = course.id,
            ).also { module ->
                // finished lesson to revert
                dataHelper.getLesson(
                    id = 10.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 100.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 40,
                        entityModifier = { it.finish(now = "2025-01-01T06:00:00Z".toInstant()) }, // 01.01. 06:00
                    )
                }

                // unfinished lesson
                dataHelper.getLesson(
                    id = 11.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 111.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                    )
                }
            }
        }

        underTest.handle(
            UserRevertsFinishLessonCommand(
                userId = user.id,
                lessonId = 10.toUUID(),
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 2

            first { it.id == 100.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 10.toUUID()
                seconds shouldBe 40
                finished shouldBe false
                finishedAt shouldBe null
            }

            first { it.id == 111.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 11.toUUID()
                seconds shouldBe 20
                finished shouldBe false
                finishedAt shouldBe null
            }
        }

        courseModuleProgressRepository.findAll().size shouldBe 0
        courseProgressRepository.findAll().size shouldBe 0
    }

    @Test
    fun `should revert finished lesson when whole course is finished`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        // 1 course, 1 module, 2 lessons - everything is finished
        dataHelper.getCourse(
            id = 0.toUUID(),
            traderId = dataHelper.getTrader(id = 0.toUUID()).id,
        ).also { course ->
            dataHelper.getCourseProgress(
                id = 0.toUUID(),
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-01-01T10:00:00Z".toInstant(), // 01.01. 10:00
            )

            dataHelper.getCourseModule(
                id = 1.toUUID(),
                courseId = course.id,
            ).also { module ->
                dataHelper.getCourseModuleProgress(
                    id = 1.toUUID(),
                    userId = user.id,
                    courseModuleId = module.id,
                    finishedAt = "2025-01-01T10:00:00Z".toInstant(), // 01.01. 10:00
                )

                // first finished lesson to revert
                dataHelper.getLesson(
                    id = 10.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 100.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 40,
                        entityModifier = { it.finish(now = "2025-01-01T06:00:00Z".toInstant()) }, // 01.01. 06:00
                    )
                }

                // second finished lesson
                dataHelper.getLesson(
                    id = 11.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 111.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                        entityModifier = { it.finish(now = "2025-01-01T08:00:00Z".toInstant()) }, // 01.01. 08:00
                    )
                }
            }
        }

        underTest.handle(
            UserRevertsFinishLessonCommand(
                userId = user.id,
                lessonId = 10.toUUID(),
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 2

            first { it.id == 100.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 10.toUUID()
                seconds shouldBe 40
                finished shouldBe false
                finishedAt shouldBe null
            }

            first { it.id == 111.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 11.toUUID()
                seconds shouldBe 20
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T08:00:00Z".toInstant() // 01.01. 08:00
            }
        }

        courseModuleProgressRepository.findAll().size shouldBe 0
        courseProgressRepository.findAll().size shouldBe 0
    }

    @Test
    fun `should revert finished lesson progress and delete course progress and one course module progress`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        // 1 course, 2 modules, each module has one finished lesson
        dataHelper.getCourse(
            id = 0.toUUID(),
            traderId = dataHelper.getTrader(id = 0.toUUID()).id,
        ).also { course ->
            dataHelper.getCourseProgress(
                id = 0.toUUID(),
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-01-01T10:00:00Z".toInstant(), // 01.01. 10:00
            )

            // first module with lesson to revert
            dataHelper.getCourseModule(
                id = 1.toUUID(),
                courseId = course.id,
            ).also { module ->
                dataHelper.getCourseModuleProgress(
                    id = 1.toUUID(),
                    userId = user.id,
                    courseModuleId = module.id,
                    finishedAt = "2025-01-01T08:00:00Z".toInstant(), // 01.01. 08:00
                )

                // finished lesson to revert
                dataHelper.getLesson(
                    id = 10.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 100.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                        entityModifier = { it.finish(now = "2025-01-01T06:00:00Z".toInstant()) }, // 01.01. 06:00
                    )
                }
            }

            // second module with finished lesson
            dataHelper.getCourseModule(
                id = 2.toUUID(),
                courseId = course.id,
            ).also { module ->
                dataHelper.getCourseModuleProgress(
                    id = 2.toUUID(),
                    userId = user.id,
                    courseModuleId = module.id,
                    finishedAt = "2025-01-01T10:00:00Z".toInstant(), // 01.01. 10:00
                )

                // finished lesson
                dataHelper.getLesson(
                    id = 20.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 200.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                        entityModifier = { it.finish(now = "2025-01-01T09:00:00Z".toInstant()) }, // 01.01. 09:00
                    )
                }
            }
        }

        underTest.handle(
            UserRevertsFinishLessonCommand(
                userId = user.id,
                lessonId = 10.toUUID(),
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 2

            first { it.id == 100.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 10.toUUID()
                seconds shouldBe 20
                finished shouldBe false
                finishedAt shouldBe null
            }

            first { it.id == 200.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 20.toUUID()
                seconds shouldBe 20
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T09:00:00Z".toInstant() // 01.01. 09:00
            }
        }

        courseModuleProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                userId shouldBe user.id
                courseModuleId shouldBe 2.toUUID()
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
            }
        }

        courseProgressRepository.findAll().size shouldBe 0
    }

    @Test
    fun `should throw if lesson does not exist`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                UserRevertsFinishLessonCommand(
                    lessonId = 0.toUUID(),
                    userId = user.id,
                ),
            )
        }
    }

    @Test
    fun `should do nothing if lesson progress does not exist`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = dataHelper.getTrader(id = 0.toUUID()).id)
        val module = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 0.toUUID(), courseModuleId = module.id)

        underTest.handle(
            UserRevertsFinishLessonCommand(
                lessonId = lesson.id,
                userId = user.id,
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 0
        }
    }
}
