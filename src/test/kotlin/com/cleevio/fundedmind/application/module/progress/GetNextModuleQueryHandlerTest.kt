package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.query.GetNextModuleToWatchQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseIsLockedForUserException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFinishedException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetNextModuleQueryHandlerTest(
    @Autowired private val underTest: GetNextModuleQueryHandler,

) : IntegrationTest() {

    @Test
    fun `should return next module`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // module that first in course (will not be return because it is before the finished one)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id, listingOrder = 1).also {
            dataHelper.getLesson(id = 11.toUUID(), courseModuleId = it.id, listingOrder = 1)
        }

        // module that was finished (and system is looking for the next one)
        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        val finishedModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id, listingOrder = 2).also {
            dataHelper.getLesson(id = 21.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = it.id,
                    entityModifier = { it.finish(finishedAt) },
                )
            }
            dataHelper.getCourseModuleProgress(userId = user1.id, courseModuleId = it.id, finishedAt = finishedAt)
        }

        // module that has coming soon flag will be skipped
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = course.id, listingOrder = 3, comingSoon = true).also {
            dataHelper.getLesson(id = 31.toUUID(), courseModuleId = it.id, listingOrder = 1)
        }

        // module that was deleted will be skipped
        dataHelper.getCourseModule(
            id = 4.toUUID(),
            courseId = course.id,
            listingOrder = 4,
            entityModifier = { it.softDelete() },
        ).also {
            dataHelper.getLesson(id = 41.toUUID(), courseModuleId = it.id, entityModifier = { it.softDelete() })
        }

        // module that is to be returned next - has 1 finished lesson, 1 in progress lesson and 1 not started lesson
        dataHelper.getCourseModule(id = 5.toUUID(), title = "5", courseId = course.id, listingOrder = 5).also {
            dataHelper.getLesson(id = 51.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                // finished lesson
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = it.id,
                    entityModifier = { it.finish() },
                )
            }
            // this lesson is in progress
            dataHelper.getLesson(id = 52.toUUID(), courseModuleId = it.id, listingOrder = 2).also {
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = it.id,
                    seconds = 10,
                    updatedTimestamp = "2025-01-02T10:00:00Z".toInstant(), // 02.01. 10:00
                )
            }
            // this lesson has not started yet
            dataHelper.getLesson(id = 53.toUUID(), courseModuleId = it.id, listingOrder = 3)
        }

        // when
        val result = underTest.handle(
            GetNextModuleToWatchQuery(
                userId = user1.id,
                finishedModuleId = finishedModule.id,
            ),
        )

        // then
        result.nextModule shouldNotBe null
        result.nextModule!!.run {
            moduleId shouldBe 5.toUUID()
            moduleTitle shouldBe "5"
            firstLessonId shouldBe 52.toUUID()
        }
    }

    @Test
    fun `should not return next module if there is no unfinished module after the current module`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // module that first in course (will not be return because it is before the finished one)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id, listingOrder = 1).also {
            dataHelper.getLesson(id = 11.toUUID(), courseModuleId = it.id, listingOrder = 1)
        }

        // module that was finished (and system is looking for the next one)
        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        val finishedModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id, listingOrder = 2).also {
            dataHelper.getLesson(id = 21.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = it.id,
                    entityModifier = { it.finish(finishedAt) },
                )
            }
            dataHelper.getCourseModuleProgress(userId = user1.id, courseModuleId = it.id, finishedAt = finishedAt)
        }

        // when
        val result = underTest.handle(
            GetNextModuleToWatchQuery(
                userId = user1.id,
                finishedModuleId = finishedModule.id,
            ),
        )

        // then
        // next module is null even though there is one before the finished one,
        // but we are looking only for the next one and previous are not considered
        result.nextModule shouldBe null
    }

    @Test
    fun `should not return next module if there is no lesson to watch`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // module that was finished (and system is looking for the next one)
        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        val finishedModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id, listingOrder = 1).also {
            dataHelper.getLesson(id = 11.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                dataHelper.getLessonProgress(
                    userId = user1.id,
                    lessonId = it.id,
                    entityModifier = { it.finish(finishedAt) },
                )
            }
            dataHelper.getCourseModuleProgress(userId = user1.id, courseModuleId = it.id, finishedAt = finishedAt)
        }

        // module that would be returned next but it has no lessons to watch
        dataHelper.getCourseModule(id = 5.toUUID(), title = "5", courseId = course.id, listingOrder = 5)

        // when
        val result = underTest.handle(
            GetNextModuleToWatchQuery(
                userId = user1.id,
                finishedModuleId = finishedModule.id,
            ),
        )

        // then
        result.nextModule shouldBe null
    }

    @Test
    fun `should throw if course module is not accessible to user`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.BASECAMP)
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val courseModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id).also {
            dataHelper.getLesson(id = 3.toUUID(), courseModuleId = it.id)
        }

        // when/then
        shouldThrow<CourseIsLockedForUserException> {
            underTest.handle(
                GetNextModuleToWatchQuery(
                    userId = user1.id,
                    finishedModuleId = courseModule.id,
                ),
            )
        }
    }

    @Test
    fun `should throw if module was not finished`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        val unfinishedModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id).also {
            dataHelper.getLesson(id = 11.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                dataHelper.getLessonProgress(userId = user1.id, lessonId = it.id, seconds = 10)
            }
        }

        // when/then
        shouldThrow<CourseModuleNotFinishedException> {
            underTest.handle(
                GetNextModuleToWatchQuery(
                    userId = user1.id,
                    finishedModuleId = unfinishedModule.id,
                ),
            )
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
