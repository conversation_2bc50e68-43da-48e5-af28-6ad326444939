package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.query.GetMeetingInCalendarQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.exception.EntityIsDeletedException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetMeetingInCalendarQueryHandlerTest(
    @Autowired private val underTest: GetMeetingInCalendarQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get meeting in calendar - verify mappings`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        // trader 1
        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "John",
            lastName = "Doe",
            entityModifier = {
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url1",
                        compressedFileUrl = "url1-comp",
                        blurHash = "1",
                    ).id,
                )
            },
        )

        // trader 2
        dataHelper.getTrader(
            id = 2.toUUID(),
            position = "Mentor",
            firstName = "Jane",
            lastName = "Doe",
            entityModifier = {
                it.changeProfilePicture(
                    fileId = dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url2",
                        compressedFileUrl = "url2-comp",
                        blurHash = "2",
                    ).id,
                )
            },
        )

        // trader 3
        dataHelper.getTrader(
            id = 3.toUUID(),
            position = "Mentor",
            firstName = "Jose",
            lastName = "Dolores",
        )

        // meeting
        dataHelper.getMeeting(
            id = 1.toUUID(),
            name = "Meeting 1",
            color = Color.BLUE,
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            description = "Meeting description",
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = true,
            meetingUrl = "meeting-url",
            recordingUrl = "recording-url",
            traderIds = listOf(3.toUUID(), 1.toUUID(), 2.toUUID()),
            entityModifier = {
                it.changeCoverPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.MEETING_COVER_PHOTO,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )

        val result = underTest.handle(
            GetMeetingInCalendarQuery(
                userId = 1.toUUID(),
                meetingId = 1.toUUID(),
            ),
        )

        // then
        result.run {
            meetingId shouldBe 1.toUUID()
            name shouldBe "Meeting 1"
            color shouldBe Color.BLUE
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            description shouldBe "Meeting description"
            invitedTiers shouldBe listOf(StudentTier.EXCLUSIVE)
            invitedDiscordUsers shouldBe true
            coverPhoto shouldNotBe null
            coverPhoto!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "123"
            }
            isLockedForMe shouldBe false
            unlockedData shouldNotBe null
            unlockedData!!.run {
                meetingUrl shouldBe "meeting-url"
                recordingUrl shouldBe "recording-url"
            }
            traders shouldHaveSize 3
            traders.run {
                this[0].run {
                    traderId shouldBe 3.toUUID()
                    displayOrder shouldBe 1
                    position shouldBe "Mentor"
                    firstName shouldBe "Jose"
                    lastName shouldBe "Dolores"
                    profilePicture shouldBe null
                }

                this[1].run {
                    traderId shouldBe 1.toUUID()
                    displayOrder shouldBe 2
                    position shouldBe "Mentor"
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    profilePicture shouldNotBe null
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url1"
                        imageCompressedUrl shouldBe "url1-comp"
                        imageBlurHash shouldBe "1"
                    }
                }

                this[2].run {
                    traderId shouldBe 2.toUUID()
                    displayOrder shouldBe 3
                    position shouldBe "Mentor"
                    firstName shouldBe "Jane"
                    lastName shouldBe "Doe"
                    profilePicture shouldNotBe null
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url2"
                        imageCompressedUrl shouldBe "url2-comp"
                        imageBlurHash shouldBe "2"
                    }
                }
            }
        }
    }

    @Test
    fun `should throw if meeting is deleted`() {
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getMeeting(id = 1.toUUID(), entityModifier = { it.softDelete() })

        shouldThrow<EntityIsDeletedException> {
            underTest.handle(
                GetMeetingInCalendarQuery(
                    userId = 1.toUUID(),
                    meetingId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should lock meeting for student without Discord when only Discord users invited`() {
        // given
        val user = dataHelper.getAppUser(id = 100.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }
        val meeting = dataHelper.getMeeting(
            id = 200.toUUID(),
            invitedTiers = emptyList(),
            invitedDiscordUsers = true,
        )

        // when
        val result = underTest.handle(
            GetMeetingInCalendarQuery(
                userId = user.id,
                meetingId = meeting.id,
            ),
        )

        // then
        result.isLockedForMe shouldBe true
        result.unlockedData shouldBe null
    }

    @Test
    fun `should unlock meeting for student with Discord when only Discord users invited`() {
        // given
        val user = dataHelper.getAppUser(id = 101.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.BASECAMP,
                entityModifier = { it.activateDiscordSubscription(expiresAt = "2099-01-01T00:00:00Z".toInstant()) },
            )
        }
        val meeting = dataHelper.getMeeting(
            id = 201.toUUID(),
            invitedTiers = emptyList(),
            invitedDiscordUsers = true,
        )

        // when
        val result = underTest.handle(
            GetMeetingInCalendarQuery(
                userId = user.id,
                meetingId = meeting.id,
            ),
        )

        // then
        result.isLockedForMe shouldBe false
        result.unlockedData shouldNotBe null
    }

    @Test
    fun `should lock meeting for Basecamp student when only Masterclass invited`() {
        // given
        val user = dataHelper.getAppUser(id = 102.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }
        val meeting = dataHelper.getMeeting(
            id = 202.toUUID(),
            invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
        )

        // when
        val result = underTest.handle(
            GetMeetingInCalendarQuery(
                userId = user.id,
                meetingId = meeting.id,
            ),
        )

        // then
        result.isLockedForMe shouldBe true
        result.unlockedData shouldBe null
    }

    @Test
    fun `should unlock meeting for Masterclass student when only Masterclass invited`() {
        // given
        val user = dataHelper.getAppUser(id = 103.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }
        val meeting = dataHelper.getMeeting(
            id = 203.toUUID(),
            invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
        )

        // when
        val result = underTest.handle(
            GetMeetingInCalendarQuery(
                userId = user.id,
                meetingId = meeting.id,
            ),
        )

        // then
        result.isLockedForMe shouldBe false
        result.unlockedData shouldNotBe null
    }
}
