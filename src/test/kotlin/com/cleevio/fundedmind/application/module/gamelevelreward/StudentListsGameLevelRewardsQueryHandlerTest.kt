package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelreward.query.StudentListsGameLevelRewardsQuery
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentListsGameLevelRewardsQueryHandlerTest(
    @Autowired private val underTest: StudentListsGameLevelRewardsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list all published game level rewards for student with blurred rewards for higher levels`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), gameLevel = GameLevel.SEVEN).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        dataHelper.getGameLevelReward(
            id = 1.toUUID(),
            listingOrder = 1,
            name = "Level 3",
            gameLevel = GameLevel.THREE,
            type = GameLevelRewardType.PHYSICAL,
            description = "Level reward description 1",
            rewardCouponCode = "REWARD123",
            rewardButton = AppButtonWithLink("Claim Reward 1", Color.BLUE, "https://example.com/claim1"),
            entityModifier = {
                it.changeRewardPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.GAME_LEVEL_REWARD_PHOTO,
                        originalFileUrl = "reward-photo-url-1",
                        compressedFileUrl = "reward-photo-url-comp-1",
                        blurHash = "789",
                    ).id,
                )
                it.publish()
            },
        )

        dataHelper.getGameLevelReward(
            id = 2.toUUID(),
            listingOrder = 2,
            name = "Level 4",
            gameLevel = GameLevel.FOUR,
            description = "Level reward description 2",
            rewardCouponCode = null,
            rewardButton = AppButtonWithLink("Claim Reward 2", Color.RED, "https://example.com/claim2"),
            entityModifier = {
                it.changeRewardPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.GAME_LEVEL_REWARD_PHOTO,
                        originalFileUrl = "reward-photo-url-2",
                        compressedFileUrl = "reward-photo-url-comp-2",
                        blurHash = "123",
                    ).id,
                )
                it.publish()
            },
        )

        // Create fourth reward (unpublished - should not be included in results)
        dataHelper.getGameLevelReward(
            id = 3.toUUID(),
            listingOrder = 4,
            name = "Level 5",
            gameLevel = GameLevel.FIVE,
        )

        // when
        val result = underTest.handle(
            StudentListsGameLevelRewardsQuery(
                studentId = student.id,
            ),
        )

        // then
        result.data.map { it.gameLevelRewardId } shouldContainExactly listOf(1.toUUID(), 2.toUUID())

        result.data.first { it.gameLevelRewardId == 1.toUUID() }.run {
            listingOrder shouldBe 1
            name shouldBe "Level 3"
            gameLevel shouldBe GameLevel.THREE
            type shouldBe GameLevelRewardType.PHYSICAL
            rewardPhoto shouldNotBe null
            rewardPhoto!!.run {
                imageOriginalUrl shouldBe "reward-photo-url-1"
                imageCompressedUrl shouldBe "reward-photo-url-comp-1"
                imageBlurHash shouldBe "789"
            }
            description shouldBe "Level reward description 1"
            rewardCouponCode shouldBe "REWARD123"
            rewardButton!!.run {
                text shouldBe "Claim Reward 1"
                color shouldBe Color.BLUE
                linkUrl shouldBe "https://example.com/claim1"
            }
        }

        result.data.first { it.gameLevelRewardId == 2.toUUID() }.run {
            listingOrder shouldBe 2
            name shouldBe "Level 4"
            gameLevel shouldBe GameLevel.FOUR
            type shouldBe GameLevelRewardType.ONLINE
            rewardPhoto shouldNotBe null
            rewardPhoto!!.run {
                imageOriginalUrl shouldBe "reward-photo-url-2"
                imageCompressedUrl shouldBe "reward-photo-url-comp-2"
                imageBlurHash shouldBe "123"
            }
            description shouldBe "Level reward description 2"
            rewardCouponCode shouldBe null
            rewardButton!!.run {
                text shouldBe "Claim Reward 2"
                color shouldBe Color.RED
                linkUrl shouldBe "https://example.com/claim2"
            }
        }
    }

    @Test
    fun `should return empty list when no published rewards exist`() {
        // given

        val student = dataHelper.getStudent(id = 1.toUUID(), gameLevel = GameLevel.ONE).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        // Create an unpublished reward (should not be included in results)
        dataHelper.getGameLevelReward(
            id = 4.toUUID(),
            entityModifier = {
                it.changeRewardPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.GAME_LEVEL_REWARD_PHOTO,
                        originalFileUrl = "reward-photo-url-4",
                        compressedFileUrl = "reward-photo-url-comp-4",
                        blurHash = "999",
                    ).id,
                )
                // Not publishing this reward
            },
        )

        // when
        val result = underTest.handle(
            StudentListsGameLevelRewardsQuery(
                studentId = student.id,
            ),
        )

        // then
        result.data shouldHaveSize 0
    }

    @Test
    fun `should blur data if student has not reached the level yet`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), gameLevel = GameLevel.ONE).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        dataHelper.getGameLevelReward(
            id = 1.toUUID(),
            listingOrder = 1,
            name = "Level 3",
            gameLevel = GameLevel.THREE,
            type = GameLevelRewardType.PHYSICAL,
            description = "Level reward description 1",
            rewardCouponCode = "REWARD123",
            rewardButton = AppButtonWithLink("Claim Reward 1", Color.BLUE, "https://example.com/claim1"),
            entityModifier = {
                it.changeRewardPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.GAME_LEVEL_REWARD_PHOTO,
                        originalFileUrl = "reward-photo-url-1",
                        compressedFileUrl = "reward-photo-url-comp-1",
                        blurHash = "789",
                    ).id,
                )
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            StudentListsGameLevelRewardsQuery(
                studentId = student.id,
            ),
        )

        // then
        result.data shouldHaveSize 1
        result.data.first().run {
            gameLevelRewardId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            name shouldBe "Level 3"
            gameLevel shouldBe GameLevel.THREE
            type shouldBe GameLevelRewardType.PHYSICAL
            rewardPhoto shouldNotBe null
            rewardPhoto!!.run {
                imageOriginalUrl shouldBe "reward-photo-url-1"
                imageCompressedUrl shouldBe "reward-photo-url-comp-1"
                imageBlurHash shouldBe "789"
            }
            description shouldBe "Level reward description 1"
            rewardCouponCode shouldBe null // blurred
            rewardButton shouldBe null // blurred
        }
    }
}
