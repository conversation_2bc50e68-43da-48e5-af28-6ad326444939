include:
  - project: devops/ci-templates/templates
    ref: main
    file: backend/backend-ci-templates-with-dependency-proxy.yml
  - project: devops/ci-templates/templates
    ref: main
    file: deploy.yml

services:
  - name: docker:27.3.1-dind
    command: [ "--tls=false" ]

variables:
  JDK_VERSION: 21

stages:
  - test
  - code-analysis
  - artifacts
  - build
  - deploy

test:
  extends: .test

test-coverage:
  extends: .test-coverage

artifacts:
  image: eclipse-temurin:21
  extends: .artifacts
  stage: artifacts
  variables:
    JAR_PATH: target/funded-mind-api-0.0.1.jar
  only:
    - main
    - staging
    - production

build:
  extends: .docker-build

deploy-devel:
  stage: deploy
  extends: .deploy-devel-stage
  variables:
    VERSION: devel
    NAMESPACE: ${CI_PROJECT_NAME}-${VERSION}
    HELM_NAME: ${CI_PROJECT_NAME}-${VERSION}
    HELM_VALUES_FILE: ./.helm/devel.values.yaml
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/funded-mind-api/main
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
    HELM_RELEASE_NAME: ${CI_PROJECT_NAME}-${VERSION}
  only:
    refs:
      - main
  environment:
    name: ${VERSION}
    url: https://api.fundedmind.devel.cleevio.dev
  needs:
    - build

deploy-staging:
  stage: deploy
  extends: .deploy-devel-stage
  variables:
    VERSION: staging
    NAMESPACE: ${CI_PROJECT_NAME}-${VERSION}
    HELM_NAME: ${CI_PROJECT_NAME}-${VERSION}
    HELM_VALUES_FILE: ./.helm/staging.values.yaml
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/funded-mind-api/staging
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
    HELM_RELEASE_NAME: ${CI_PROJECT_NAME}-${VERSION}
  only:
    refs:
      - staging
  environment:
    name: ${VERSION}
    url: https://api.fundedmind.staging.cleevio.dev
  needs:
    - build

deploy-production:
  stage: deploy
  extends: .deploy-cleevio-infrastructure-prod
  when: manual
  variables:
    VERSION: production
    NAMESPACE: ${CI_PROJECT_NAME}-${VERSION}
    HELM_NAME: ${CI_PROJECT_NAME}-${VERSION}
    HELM_VALUES_FILE: ./.helm/production.values.yaml
    HELM_VERSION: 0.0.34
    IMAGE_REPOSITORY: gitlab.cleevio.cz:4567/backend/funded-mind-api/production
    IMAGE_TAG: ${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
    HELM_RELEASE_NAME: ${CI_PROJECT_NAME}-${VERSION}
  only:
    refs:
      - production
  environment:
    name: ${VERSION}
    url: https://api.fundedmind.cz
  needs:
    - build