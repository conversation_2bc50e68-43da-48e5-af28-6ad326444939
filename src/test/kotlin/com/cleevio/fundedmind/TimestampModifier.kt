package com.cleevio.fundedmind

import jakarta.persistence.EntityManager
import org.jooq.DSLContext
import org.jooq.Table
import org.jooq.impl.DSL.field
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Component
class TimestampModifier(
    private val entityManager: EntityManager,
    private val dslContext: DSLContext,
) {

    @Transactional
    fun setUpdatedAt(
        table: Table<*>,
        id: UUID,
        timestamp: Instant,
    ) {
        dslContext
            .update(table)
            .set(field("updated_at"), timestamp)
            .where(field("id").eq(id))
            .execute()
    }

    @Transactional
    fun setCreatedAt(
        table: Table<*>,
        id: UUID,
        timestamp: Instant,
    ) {
        dslContext
            .update(table)
            .set(field("created_at"), timestamp)
            .where(field("id").eq(id))
            .execute()
    }
}
