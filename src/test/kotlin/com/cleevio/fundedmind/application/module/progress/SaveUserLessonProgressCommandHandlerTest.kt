package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.command.SaveUserLessonProgressCommand
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.domain.progress.LessonProgressRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired

class SaveUserLessonProgressCommandHandlerTest(
    @Autowired private val underTest: SaveUserLessonProgressCommandHandler,
    @Autowired private val lessonProgressRepository: LessonProgressRepository,
) : IntegrationTest() {
    @Test
    fun `should create a new lesson progress`() {
        val trader = dataHelper.getTrader(id = 0.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val user = dataHelper.getAppUser(id = 999.toUUID())
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)

        underTest.handle(
            SaveUserLessonProgressCommand(
                userId = user.id,
                lessonId = lesson.id,
                seconds = 10,
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                userId shouldBe 999.toUUID()
                lessonId shouldBe 555.toUUID()
                seconds shouldBe 10
                finished shouldBe false
                finishedAt shouldBe null
            }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [0, 100, 999])
    fun `should update lesson progress`(newSeconds: Int) {
        val trader = dataHelper.getTrader(id = 0.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val user = dataHelper.getAppUser(id = 999.toUUID())
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)

        dataHelper.getLessonProgress(
            id = 1.toUUID(),
            userId = user.id,
            lessonId = lesson.id,
            seconds = 420,
        )

        underTest.handle(
            SaveUserLessonProgressCommand(
                userId = user.id,
                lessonId = lesson.id,
                seconds = newSeconds,
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                id shouldBe 1.toUUID()
                userId shouldBe 999.toUUID()
                lessonId shouldBe 555.toUUID()
                seconds shouldBe newSeconds
                finished shouldBe false
                finishedAt shouldBe null
            }
        }
    }

    @Test
    fun `should update correct lesson progress`() {
        val trader = dataHelper.getTrader(id = 0.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val user = dataHelper.getAppUser(id = 999.toUUID())

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id).also { lesson ->
            dataHelper.getLessonProgress(
                id = 10.toUUID(),
                userId = user.id,
                lessonId = lesson.id,
                seconds = 420,
            )
        }

        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule.id).also { lesson ->
            dataHelper.getLessonProgress(
                id = 20.toUUID(),
                userId = user.id,
                lessonId = lesson.id,
                seconds = 310,
            )
        }

        underTest.handle(
            SaveUserLessonProgressCommand(
                userId = user.id,
                lessonId = 2.toUUID(),
                seconds = 900,
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 2
            first { it.id == 10.toUUID() }.seconds shouldBe 420
            first { it.id == 20.toUUID() }.seconds shouldBe 900
        }
    }

    @Test
    fun `should throw if lesson does not exist`() {
        val user = dataHelper.getAppUser(id = 999.toUUID())

        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                SaveUserLessonProgressCommand(
                    userId = user.id,
                    lessonId = 111.toUUID(),
                    seconds = 420,
                ),
            )
        }

        lessonProgressRepository.findAll().size shouldBe 0
    }
}
