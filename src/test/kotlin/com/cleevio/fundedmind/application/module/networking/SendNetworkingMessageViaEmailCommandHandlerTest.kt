package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.networking.command.SendNetworkingMessageViaEmailCommand
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserNotFoundException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasNoActiveDiscordException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasWrongStudentTierException
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.networking.NetworkingMessageRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant

class SendNetworkingMessageViaEmailCommandHandlerTest(
    @Autowired private val underTest: SendNetworkingMessageViaEmailCommandHandler,
    @Autowired private val networkingMessageRepository: NetworkingMessageRepository,
) : IntegrationTest() {

    @Test
    fun `should create networking message and publish event when sending message between students`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Sender",
                studentTier = StudentTier.MASTERCLASS,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "Hello, I would like to connect with you!",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "Hello, I would like to connect with you!"
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should create networking message when sending message from student to trader`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.EXCLUSIVE,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(id = it.id, firstName = "Jane", lastName = "Trader")
        }

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "I'm interested in your mentoring services.",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "I'm interested in your mentoring services."
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should create networking message when sending message from trader to student`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER).also {
            dataHelper.getTrader(id = it.id, firstName = "John", lastName = "Trader")
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Student")
        }

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "Thank you for your interest in my services.",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "Thank you for your interest in my services."
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should fetch existing messages between users where sender and recipient are the same`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Sender",
                studentTier = StudentTier.MASTERCLASS,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        // Create some existing messages
        dataHelper.getNetworkingMessage(
            id = 10.toUUID(),
            senderUserId = sendingUser.id,
            recipientUserId = recipientUser.id,
            text = "First message",
        )

        dataHelper.getNetworkingMessage(
            id = 11.toUUID(),
            senderUserId = recipientUser.id,
            recipientUserId = sendingUser.id,
            text = "Reply message",
        )

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "New message in conversation",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "New message in conversation"
        }

        // Verify that we can find all messages between these users
        val allMessages = networkingMessageRepository.findAllBySenderUserIdAndRecipientUserId(
            senderUserId = sendingUser.id,
            recipientUserId = recipientUser.id,
        )
        allMessages.size shouldBe 2

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should throw exception when sending user does not exist`() {
        // given
        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        // when & then
        shouldThrow<UserNotFoundException> {
            underTest.handle(
                SendNetworkingMessageViaEmailCommand(
                    senderUserId = 999.toUUID(), // non-existent user
                    recipientUserId = recipientUser.id,
                    text = "This should fail",
                ),
            )
        }
    }

    @Test
    fun `should throw exception when recipient user does not exist`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Sender",
                studentTier = StudentTier.MASTERCLASS,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        // when & then
        shouldThrow<UserNotFoundException> {
            underTest.handle(
                SendNetworkingMessageViaEmailCommand(
                    senderUserId = sendingUser.id,
                    recipientUserId = 999.toUUID(), // non-existent user
                    text = "This should fail",
                ),
            )
        }
    }

    @Test
    fun `should handle admin user as sender`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Student")
        }

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "Admin message to student",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "Admin message to student"
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should handle admin user as recipient`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.EXCLUSIVE,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.ADMIN)

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "Student message to admin",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "Student message to admin"
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should throw exception when student sender has BASECAMP tier`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.BASECAMP, // BASECAMP tier should be rejected
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        // when & then
        shouldThrow<StudentHasWrongStudentTierException> {
            underTest.handle(
                SendNetworkingMessageViaEmailCommand(
                    senderUserId = sendingUser.id,
                    recipientUserId = recipientUser.id,
                    text = "This should fail due to wrong tier",
                ),
            )
        }
    }

    @Test
    fun `should throw exception when student sender has no active Discord subscription`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.MASTERCLASS, // Correct tier but no Discord
            )
            // Note: No Discord subscription activation
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        // when & then
        shouldThrow<StudentHasNoActiveDiscordException> {
            underTest.handle(
                SendNetworkingMessageViaEmailCommand(
                    senderUserId = sendingUser.id,
                    recipientUserId = recipientUser.id,
                    text = "This should fail due to no Discord",
                ),
            )
        }
    }

    @Test
    fun `should throw exception when student sender has BASECAMP tier and no Discord subscription`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.BASECAMP, // Wrong tier
            )
            // Note: No Discord subscription activation
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        // when & then
        // Should fail on tier check first (since that's checked before Discord)
        shouldThrow<StudentHasWrongStudentTierException> {
            underTest.handle(
                SendNetworkingMessageViaEmailCommand(
                    senderUserId = sendingUser.id,
                    recipientUserId = recipientUser.id,
                    text = "This should fail due to wrong tier",
                ),
            )
        }
    }

    @Test
    fun `should allow MASTERCLASS student with active Discord to send message`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.MASTERCLASS,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "MASTERCLASS with Discord should work",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "MASTERCLASS with Discord should work"
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }

    @Test
    fun `should allow EXCLUSIVE student with active Discord to send message`() {
        // given
        val sendingUser = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Student",
                studentTier = StudentTier.EXCLUSIVE,
            ) { student ->
                student.activateDiscordSubscription(Instant.now().plusSeconds(3600))
            }
        }

        val recipientUser = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", lastName = "Recipient")
        }

        every { sendEmailService.sendNetworkingMessage(any()) } just Runs

        // when
        val result = underTest.handle(
            SendNetworkingMessageViaEmailCommand(
                senderUserId = sendingUser.id,
                recipientUserId = recipientUser.id,
                text = "EXCLUSIVE with Discord should work",
            ),
        )

        // then
        networkingMessageRepository.getReferenceById(result.id).run {
            senderUserId shouldBe 1.toUUID()
            recipientUserId shouldBe 2.toUUID()
            text shouldBe "EXCLUSIVE with Discord should work"
        }

        verify {
            sendEmailService.sendNetworkingMessage(networkingMessageId = result.id)
        }
    }
}
