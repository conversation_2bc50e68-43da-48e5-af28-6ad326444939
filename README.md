# FundedMind API

## Calendly
- Neocakavajte prijemne developer <PERSON><PERSON> a<PERSON> u <PERSON>e
- "public" a "developer" cast<PERSON>
  - "developer" cast moze byt pod inym uctom a moze mat viacero appiek 
  - takze jeden dev ucet moze spravovat viacero environmentov
- Vytvorenie noveho `personal access token` na https://calendly.com/integrations/api_webhooks
  - v "public" casti 
  - je to v podstate JWT tokean, pouziva sa potom na auth requestov
- Ziskanie info o userovi a organizacii - https://developer.calendly.com/api-docs/005832c83aeae-get-current-user

## Project

- project has tight budget, from financial standpoint it does not deserve too much attention and love
  - **ACTION**: reduce reviews and automated tests
- email templates are found in: https://fundedmind.devel.cleevio.dev/api/email-template/list

## Flows

### Onboarding

- user registers on FundedMind via Firebase
- after confirmation, FE invokes `POST /users/sign-up` endpoint to create new user in DB
  - this also creates `Student` entity
- during onboarding, FE may:
  - upload profile picture (`uploadProfilePicture`)
  - fill out survey and save it (`saveOnboardSurvey`)
  - pay for masterclass tier (`initiateUpgradeToMasterclass`)
- once onboarding is complete, FE invokes `finishOnboarding` to finalize `Student` entity

## Tech

- Implicit Join Paths **JOOQ**
  - naming follows foreign key naming, so always specify the foreign key name
  - to instruct jooq to generate implicit join paths as FK names this config prop is used:
    - `<implicitJoinPathsUseTableNameForUnambiguousFKs>false</implicitJoinPathsUseTableNameForUnambiguousFKs>`

- Stripe
  - We are using Custom Checkouts to create Stripe Checkout Sessions
  - https://docs.stripe.com/checkout/custom/quickstart?server-lang=java
  - it is still in beta, so we need to manually with `Stripe.addBetaVersion("custom_checkout_beta", "v1")`

- Firebase Storage
  - in Firebase console - open Storage
  - create new bucket
  - create folder `'public'`
  - change rules

- tables `AppUser`, `Student`, `Onboarding`, `Trader` share same `ID`
  - this allows injecting `@AuthenticationPrincipal studentId/traderId: UUID` into controller and easily fetching entity by ID
  - otherwise we would have to always join Student/Trader and AppUser tables to verify student/trader belong to authenticated user

- PostgreSQL version is in `.env` file
    - change it in `pom.xml` as well -> property `postgres.version`
    - search for `POSTGRES_VERSION` and `postgres.version`

- ask for `application-local.yml` `@radvan`
    - notify other devs on Slack if new application properties are added and should be overridden by devs locally

- always declare return type in controller functions

- Naming conventions
    - Function that asserts state and **throws exception** - `CHECK*`
        - Let's use `check` as it is the shortest out of `assert/validate/check` options :D
        - For functions that do not throw, use something like `is/has/exists/contains...`

### Stripe - Local setup
- install Stripe CLI https://docs.stripe.com/stripe-cli + use `stripe login`
- use https://dashboard.stripe.com/test/apikeys to copy secret-api-key into `integration.stripe.api-key`
- check your webhook in https://dashboard.stripe.com/test/workbench/webhooks
  - webhook has a `signing secret` that you need to copy into: `fundedmind.security.webhook.stripe.secret-key`
- you can forward all your CLI-locally-generated-events to your localhost
  - in one console start listener: `stripe listen --load-from-webhooks-api --forward-to localhost:8080`
  - in second generate events e.g.: `stripe trigger payment_intent.succeeded`
    - check the listener console for logs about the event traffic and processing
---

### Hubspot - Local setup
- is not possible to run local instance of hubspot
- for local testing use api-key for dev environment
- Hubspot API doc - https://developers.hubspot.com/docs/reference/api/crm/objects/contacts#

# Typed IDs

### Declare typed ID and usage in base entity

```kotlin
interface Identifier {
    val id: UUID
}

@MappedSuperclass
abstract class IdentifiableEntity<IDENTIFIER : Identifier>(
    @field:EmbeddedId val id: IDENTIFIER, 
) {
    val uuid: UUID
        get() = id.id

    override fun equals(other: Any?): Boolean {
        other ?: return false
        if (this === other) return true
        if (javaClass != ProxyUtils.getUserClass(other)) return false
        other as IdentifiableEntity<*>
        return this.id == other.id
    }

    override fun hashCode(): Int = id.hashCode()
}
```

---

### declare typed ID for every entity

```kotlin
@Embeddable
data class AppUserId(override val id: UUID) : Identifier

// usage in entity
@Entity
class AppUser(
  id: AppUserId = AppUserId(UUIDv7()),
  // ... other fields
) : DomainEntity<AppUserId>(id)
```

---

### custom converter for Jooq and usage in pom.xml for <forcedType>

```kotlin
/**
 * JOOQ converter for enabling direct usage of typed identifiers.
 *
 * This converter ensures JOOQ can generate valid queries when working with identifiers.
 * Without it, JOOQ would fail to compare database types with `Identifier` instances,
 * causing issues in operations like `.where()` clauses.
 *
 * Example usage in `pom.xml` JOOQ configuration:
 * 
 * <forcedType>
 *   <userType>com.cleevio.fundedmind.UserId</userType>
 *   <converter>com.cleevio.fundedmind.TypedIdentifierJooqConverter</converter>
 *   <genericConverter>true</genericConverter>
 *   <includeExpression>.*\.(?:APP_USER\.ID|APP_USER_ID)$</includeExpression>
 * </forcedType>
 * 
 */
class TypedIdentifierJooqConverter<DB_TYPE, USER_TYPE : Identifier>(
private val databaseType: Class<UUID>,
private val userType: Class<USER_TYPE>,
) : Converter<UUID, USER_TYPE> {

    override fun from(databaseObject: UUID?): USER_TYPE? = databaseObject?.let { uuid ->
        userType.getDeclaredConstructor(UUID::class.java).newInstance(uuid)
    }
    override fun to(userObject: USER_TYPE?): UUID? = userObject?.id
    override fun fromType(): Class<UUID> = databaseType
    override fun toType(): Class<USER_TYPE> = userType
}
```

---

### custom converter for handling FE input in controllers

```kotlin
/**
 * Configures a custom converter for `UUID -> Identifier`.
 *
 * This setup enables using strongly-typed identifiers (e.g., `UserId`) in `@PathVariable` and `@RequestParam`
 * instead of raw `UUID`. The converter operates on strings, as that is the format received from the frontend.
 *
 * Example usage:
 * @GetMapping("/{userId}", produces = [ApiVersion.VERSION_1_JSON])
 * fun getUser(@PathVariable userId: UUID)
 */
@Configuration
class TypedIdentifierWebConfig : WebMvcConfigurer {
  override fun addFormatters(registry: FormatterRegistry) {
    registry.addConverterFactory(UUIDStringToIdentifierConverterFactory())
  }
}

class UUIDStringToIdentifierConverterFactory : ConverterFactory<String, Identifier> {

    override fun <T : Identifier> getConverter(targetType: Class<T>): Converter<String, T> =
        StringToIdentifierConverter(targetType)

    private class StringToIdentifierConverter<T : Identifier>(
        private val targetType: Class<T>,
    ) : Converter<String, T> {
        override fun convert(source: String): T {
            val uuid = source.toUUID() // Convert the string to a UUID
            val constructor = targetType.getDeclaredConstructor(UUID::class.java)
            return constructor.newInstance(uuid) // Create an instance of the target Identifier type
        }
    }
}
```

### Additional side notes

- FE must agree to typed IDs - because they will receive and use them as an object (`"userId" : { "id" : "<UUID>" }`), 
  but swagger should generate correct classes and they will have a benefit of typed IDs on FE as well
- Downside of having typed IDs is accessing the inner field on various possibly unexpected places 
    - such as locking: `@LockFieldParameter("userId.id")`

```
Caused by: org.springframework.core.convert.ConverterNotFoundException:
No converter found capable of converting from type
        [com.cleevio.fundedmind.domain.appuser.UserId] to type [java.lang.String]
```

