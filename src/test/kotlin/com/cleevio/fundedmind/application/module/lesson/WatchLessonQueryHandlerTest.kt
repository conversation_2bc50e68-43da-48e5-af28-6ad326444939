package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.query.WatchLessonQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseIsLockedForUserException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class WatchLessonQueryHandlerTest(
    @Autowired private val underTest: WatchLessonQueryHandler,
) : IntegrationTest() {
    @Test
    fun `should get watch lesson data - verify mappings`() {
        // given
        val user1 = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val user2 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )
        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = trader.id,
            title = "Course",
            courseCategory = CourseCategory.TRADING_BASICS,
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id, title = "Module")

        // lesson with 2 attachments and progress of a user
        val lesson = dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 1,
            title = "First Lesson",
            durationInSeconds = 3600,
            videoUrl = "video-url",
            thumbnailUrl = "thumbnail-url",
            thumbnailAnimationUrl = "thumbnail-animation-url",
        ).also { lesson ->
            dataHelper.getLessonProgress(
                id = 10.toUUID(),
                userId = user1.id,
                lessonId = lesson.id,
                seconds = 100,
            )
            dataHelper.getLessonProgress(
                id = 20.toUUID(),
                userId = user2.id, // different user
                lessonId = lesson.id,
                seconds = 200,
            )
            dataHelper.getLessonAttachment(
                id = 111.toUUID(),
                displayOrder = 2,
                lessonId = lesson.id,
                name = "Attachment 111",
                type = LessonAttachmentType.PDF,
                entityModifier = {
                    it.changeAttachmentDocument(
                        dataHelper.getDocument(
                            id = 1110.toUUID(),
                            type = FileType.LESSON_ATTACHMENT,
                        ).id,
                    )
                },
            )
            dataHelper.getLessonAttachment(
                id = 112.toUUID(),
                displayOrder = 1,
                lessonId = lesson.id,
                name = "Attachment 112",
                type = LessonAttachmentType.XLSX,
                entityModifier = {
                    it.changeAttachmentDocument(
                        dataHelper.getDocument(
                            id = 1120.toUUID(),
                            type = FileType.LESSON_ATTACHMENT,
                        ).id,
                    )
                },
            )

            dataHelper.getLessonComment(lessonId = lesson.id, appUserId = user1.id).also { thread ->
                dataHelper.getLessonComment(lessonId = lesson.id, appUserId = user1.id, threadId = thread.id)
                dataHelper.getLessonComment(lessonId = lesson.id, appUserId = user2.id, threadId = thread.id)

                dataHelper.getLessonComment(
                    lessonId = lesson.id,
                    appUserId = user2.id,
                    threadId = thread.id,
                    entityModifier = { it.softDelete() },
                )
            }
            dataHelper.getLessonComment(lessonId = lesson.id, appUserId = user1.id)
            dataHelper.getLessonComment(lessonId = lesson.id, appUserId = user2.id)
        }

        val result = underTest.handle(
            WatchLessonQuery(
                userId = user1.id,
                filter = WatchLessonQuery.Filter(lessonId = lesson.id),
            ),
        )

        result.run {
            lessonId shouldBe 1.toUUID()
            title shouldBe "First Lesson"
            videoUrl shouldBe "video-url"
            description shouldBe null
            durationInSeconds shouldBe 3600
            thumbnailUrl shouldBe "thumbnail-url"
            thumbnailAnimationUrl shouldBe "thumbnail-animation-url"

            traderBio.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            }

            progress shouldNotBe null
            progress!!.run {
                seconds shouldBe 100
                finished shouldBe false
            }

            attachments shouldHaveSize 2
            // attachments are sorted by displayOrder
            attachments[0].run {
                attachmentId shouldBe 112.toUUID()
                displayOrder shouldBe 1
                nameWithExtension shouldBe "Attachment 112"
                document shouldNotBe null
                document!!.documentId shouldBe 1120.toUUID()
                type shouldBe LessonAttachmentType.XLSX
            }
            attachments[1].run {
                attachmentId shouldBe 111.toUUID()
                displayOrder shouldBe 2
                nameWithExtension shouldBe "Attachment 111"
                document shouldNotBe null
                document!!.documentId shouldBe 1110.toUUID()
                type shouldBe LessonAttachmentType.PDF
            }

            commentCount shouldBe 5 // 3 top-level comments + 2 thread comments, deleted thread comment is not included
            courseModuleId shouldBe 1.toUUID()
            courseModuleTitle shouldBe "Module"
            courseId shouldBe 1.toUUID()
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            courseTitle shouldBe "Course"
        }
    }

    @Test
    fun `should throw if lesson is not accessible to user`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val courseModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id)

        // when/then
        shouldThrow<CourseIsLockedForUserException> {
            underTest.handle(
                WatchLessonQuery(
                    userId = user.id,
                    filter = WatchLessonQuery.Filter(lessonId = lesson.id),
                ),
            )
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
