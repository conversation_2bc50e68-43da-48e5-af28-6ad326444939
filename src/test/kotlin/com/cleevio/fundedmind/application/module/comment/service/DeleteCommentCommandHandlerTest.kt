package com.cleevio.fundedmind.application.module.comment.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.comment.command.DeleteCommentCommand
import com.cleevio.fundedmind.domain.comment.CommentLikeRepository
import com.cleevio.fundedmind.domain.comment.CommentRepository
import com.cleevio.fundedmind.domain.comment.exception.CommentOwnerIsWrongException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class DeleteCommentCommandHandlerTest(
    @Autowired private val underTest: DeleteCommentCommandHandler,
    @Autowired private val commentRepository: CommentRepository,
    @Autowired private val commentLikeRepository: CommentLikeRepository,
) : IntegrationTest() {

    @Test
    fun `should delete existing comment when student is owner`() {
        // given
        dataHelper.getAppUser(id = 999.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 999.toUUID())
        val trader = dataHelper.getTrader(id = 888.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = student.id,
            lessonId = lesson.id,
        )

        // when
        underTest.handle(
            DeleteCommentCommand(
                appUserId = student.id,
                commentId = comment.id,
            ),
        )

        // then
        commentRepository.getReferenceById(comment.id).isDeleted shouldBe true
    }

    @Test
    fun `should delete existing comment when trader is owner`() {
        // given
        dataHelper.getAppUser(id = 999.toUUID(), userRole = UserRole.TRADER)
        val trader = dataHelper.getTrader(id = 999.toUUID(), commentControl = false)
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = trader.id,
            lessonId = lesson.id,
        )

        // when
        underTest.handle(
            DeleteCommentCommand(
                appUserId = trader.id,
                commentId = comment.id,
            ),
        )

        // then
        commentRepository.getReferenceById(comment.id).isDeleted shouldBe true
    }

    @Test
    fun `should delete existing comment when user is trader with commentControl`() {
        // given
        dataHelper.getAppUser(id = 999.toUUID(), userRole = UserRole.TRADER)
        val trader = dataHelper.getTrader(id = 999.toUUID(), commentControl = true)
        val otherUser = dataHelper.getAppUser(id = 888.toUUID())

        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = otherUser.id,
            lessonId = lesson.id,
        )

        // when
        underTest.handle(
            DeleteCommentCommand(
                appUserId = trader.id,

                commentId = comment.id,
            ),
        )

        // then
        commentRepository.getReferenceById(comment.id).isDeleted shouldBe true
    }

    @Test
    fun `should throw exception when user tries to delete comment of another user`() {
        // given
        val owner = dataHelper.getAppUser(id = 999.toUUID())
        val otherUser = dataHelper.getAppUser(id = 888.toUUID())
        val trader = dataHelper.getTrader(id = 999.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = owner.id,
            lessonId = lesson.id,
        )

        // when / then
        shouldThrow<CommentOwnerIsWrongException> {
            underTest.handle(
                DeleteCommentCommand(
                    appUserId = otherUser.id,
                    commentId = comment.id,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when trader without commentControl deletes comment`() {
        // given
        val owner = dataHelper.getAppUser(id = 999.toUUID())
        val trader = dataHelper.getTrader(id = 999.toUUID(), commentControl = false)
        val otherUser = dataHelper.getAppUser(id = 888.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = otherUser.id,
            lessonId = lesson.id,
        )

        // when / then
        shouldThrow<CommentOwnerIsWrongException> {
            underTest.handle(
                DeleteCommentCommand(
                    appUserId = trader.id,
                    commentId = comment.id,
                ),
            )
        }
    }

    @Test
    fun `should delete existing comment when user is admin`() {
        // given
        dataHelper.getAppUser(id = 999.toUUID(), userRole = UserRole.ADMIN)
        val otherUser = dataHelper.getAppUser(id = 888.toUUID())
        val trader = dataHelper.getTrader(id = 777.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = otherUser.id,
            lessonId = lesson.id,
        )

        // when
        underTest.handle(
            DeleteCommentCommand(
                appUserId = 999.toUUID(),
                commentId = comment.id,
            ),
        )

        // then
        commentRepository.getReferenceById(comment.id).isDeleted shouldBe true
    }

    @Test
    fun `should delete all comment likes when comment is deleted`() {
        // given
        dataHelper.getAppUser(id = 999.toUUID(), userRole = UserRole.STUDENT)
        val student = dataHelper.getStudent(id = 999.toUUID())
        val liker1 = dataHelper.getAppUser(id = 777.toUUID())
        val liker2 = dataHelper.getAppUser(id = 666.toUUID())
        val trader = dataHelper.getTrader(id = 888.toUUID())
        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        val comment = dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = student.id,
            lessonId = lesson.id,
        )

        // Create likes for the comment
        dataHelper.getCommentLike(id = 1001.toUUID(), appUserId = liker1.id, commentId = comment.id)
        dataHelper.getCommentLike(id = 1002.toUUID(), appUserId = liker2.id, commentId = comment.id)

        // Verify likes exist
        commentLikeRepository.findAllByCommentId(comment.id).size shouldBe 2

        // when
        underTest.handle(
            DeleteCommentCommand(
                appUserId = student.id,
                commentId = comment.id,
            ),
        )

        // then
        commentRepository.getReferenceById(comment.id).isDeleted shouldBe true
        commentLikeRepository.findAllByCommentId(comment.id).size shouldBe 0
    }
}
