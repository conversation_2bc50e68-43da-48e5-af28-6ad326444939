package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamepayout.StudentGetsGameProgressOverviewQueryHandler
import com.cleevio.fundedmind.application.module.gamepayout.query.StudentGetsGameProgressOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate

class StudentGetsGameProgressOverviewQueryHandlerTest(
    @Autowired private val underTest: StudentGetsGameProgressOverviewQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return student data`() {
        val location = dataHelper.getUserLocation(
            id = 100.toUUID(),
            city = "Prague",
        )
        val student = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FOUR,
                entityModifier = { it.patchLocation(location.id) },
            )
        }

        // Create approved payout documents
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2023, 6, 15),
            entityModifier = {
                it.approveAwaiting(
                    now = "2023-06-15T10:00:00Z".toInstant(),
                )
            },
        )

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("500.50"),
            payoutDate = LocalDate.of(2024, 8, 20),
            entityModifier = {
                it.approveAwaiting(
                    now = "2024-08-20T10:00:00Z".toInstant(),
                )
            },
        )

        // Create a waiting payout (should not be counted)
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("300.00"),
            payoutDate = LocalDate.of(2025, 9, 10),
        ) // state remains WAITING

        // Create a denied payout (should not be counted)
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("400.00"),
            payoutDate = LocalDate.of(2025, 9, 15),
            entityModifier = { it.denyAwaiting(message = "Rejected", now = "2025-09-15T10:00:00Z".toInstant()) },
        )

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            gameLevel shouldBe GameLevel.FOUR
            overallPayoutAmount shouldBeEqualComparingTo BigDecimal("1500.50") // 1000.00 + 500.50
        }
    }

    @Test
    fun `should return student game progress overview with zero payout amount when no approved payouts`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        // Create only waiting/denied payouts
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("100.00"),
            payoutDate = LocalDate.of(2024, 5, 10),
        ) // state remains WAITING

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("200.00"),
            payoutDate = LocalDate.of(2024, 6, 15),
            entityModifier = { it.denyAwaiting("Rejected") },
        )

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            overallPayoutAmount shouldBeEqualComparingTo BigDecimal.ZERO
        }
    }

    @Test
    fun `should return progress for student with basecamp and no payouts`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 4.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.BASECAMP,
                gameLevel = GameLevel.ZERO,
            )
        }

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            gameLevel shouldBe GameLevel.ZERO
            overallPayoutAmount shouldBeEqualComparingTo BigDecimal.ZERO
        }
    }

    @Test
    fun `should return progresses up to level five`() {
        // Create a student at level 4
        val student = dataHelper.getAppUser(id = 8.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FOUR,
            )
        }

        // Create an approved backtesting document for level 2
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
            entityModifier = {
                it.approveAwaiting(
                    now = "2023-06-15T10:00:00Z".toInstant(),
                )
            },
        )

        // Create a completed strategy course for level 2
        dataHelper.getCourse(
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader(id = 5.toUUID()).id,
        ).also { course ->
            dataHelper.getCourseProgress(
                userId = student.id,
                courseId = course.id,
                finishedAt = "2023-06-15T10:00:00Z".toInstant(),
            )
        }

        // Create a certificate document for level 3
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.CERTIFICATE,
            entityModifier = { it.approveAwaiting(now = "2023-06-15T10:00:00Z".toInstant()) },
        )

        // Create approved payout documents totaling 3000 - for level 4 (first payout) and partial progress in level 5
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("3000.00"),
            payoutDate = LocalDate.of(2023, 6, 15),
            entityModifier = {
                it.approveAwaiting(now = "2023-06-15T10:00:00Z".toInstant())
            },
        )

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            overallPayoutAmount shouldBeEqualComparingTo BigDecimal("3000.00")
            levelTwoProgress!!.run {
                backtestingState shouldBe GameDocumentApprovalState.APPROVED
                strategyModuleFinished shouldBe true
            }
            levelThreeProgress!!.run {
                certificateState shouldBe GameDocumentApprovalState.APPROVED
            }
            levelFourProgress!!.run {
                firstPayoutState shouldBe GameDocumentApprovalState.APPROVED
            }
            levelFiveProgress!!.run {
                payoutProgressInLevel shouldBeEqualComparingTo BigDecimal("3000.00")
                payoutGoal shouldBeEqualComparingTo BigDecimal("5000")
            }
            levelSixProgress shouldBe null
            levelSevenProgress shouldBe null
            levelEightProgress shouldBe null
            levelNineProgress shouldBe null
        }
    }

    @Test
    fun `should return correct payout progress for multiple levels`() {
        // Create a student at level 7
        val student = dataHelper.getAppUser(id = 9.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.SIX,
            )
        }

        // Create approved payout documents totaling 18000
        // This should cover level 5 (5000), level 6 (10000), and part of level 7 (3000/20000)
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("11000.00"),
            payoutDate = LocalDate.of(2023, 6, 15),
            entityModifier = {
                it.approveAwaiting(
                    now = "2023-06-15T10:00:00Z".toInstant(),
                )
            },
        )

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("7000.00"),
            payoutDate = LocalDate.of(2023, 7, 15),
            entityModifier = {
                it.approveAwaiting(
                    now = "2023-07-15T10:00:00Z".toInstant(),
                )
            },
        )

        // Create a denied payout document
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2023, 6, 15),
            entityModifier = {
                it.denyAwaiting(
                    message = "Invalid payout",
                    now = "2023-06-15T10:00:00Z".toInstant(),
                )
            },
        )

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            // Level 5 should be fully completed (5000/5000)
            levelFiveProgress!!.run {
                payoutProgressInLevel shouldBeEqualComparingTo BigDecimal("5000")
                payoutGoal shouldBeEqualComparingTo BigDecimal("5000")
            }

            // Level 6 should be fully completed (10000/10000)
            levelSixProgress!!.run {
                payoutProgressInLevel shouldBeEqualComparingTo BigDecimal("10000")
                payoutGoal shouldBeEqualComparingTo BigDecimal("10000")
            }

            // Level 7 should be partially completed (2000/20000)
            levelSevenProgress!!.run {
                payoutProgressInLevel shouldBeEqualComparingTo BigDecimal("18000")
                payoutGoal shouldBeEqualComparingTo BigDecimal("20000")
            }

            // Level 8 should not be available yet
            levelEightProgress shouldBe null
        }
    }

    @Test
    fun `should return levelTwoProgress with backtesting denied and strategy false for level 1 student`() {
        // Create a student at level 1 attempting to reach level 2
        val student = dataHelper.getAppUser(id = 5.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.ONE,
            )
        }

        // Create an approved backtesting document
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
            entityModifier = {
                it.denyAwaiting(
                    message = "Invalid backtesting",
                    now = "2023-06-15T10:00:00Z".toInstant(),
                )
            },
        )

        // Create a NOT completed strategy course
        dataHelper.getCourse(
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader(id = 5.toUUID()).id,
        )

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            levelTwoProgress shouldBe StudentGetsGameProgressOverviewQuery.LevelTwoProgress(
                backtestingState = GameDocumentApprovalState.DENIED,
                strategyModuleFinished = false,
            )
            levelThreeProgress shouldBe null
        }
    }

    @Test
    fun `should return levelThreeProgress with waiting state for level 2 student`() {
        // Create a student at level 2 attempting to reach level 3
        val student = dataHelper.getAppUser(id = 6.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.TWO,
            )
        }

        // Create a certificate document with WAITING state
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.CERTIFICATE,
        ) // state remains WAITING

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            levelThreeProgress shouldBe StudentGetsGameProgressOverviewQuery.LevelThreeProgress(
                certificateState = GameDocumentApprovalState.WAITING,
            )
            levelFourProgress shouldBe null
        }
    }

    @Test
    fun `should return levelFourProgress with denied first payout for level 3 student`() {
        // Create a student at level 3 attempting to reach level 4
        val student = dataHelper.getAppUser(id = 7.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        // Create a denied payout document
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2023, 6, 15),
            entityModifier = {
                it.denyAwaiting(
                    message = "Invalid payout",
                    now = "2023-06-15T10:00:00Z".toInstant(),
                )
            },
        )

        val result = underTest.handle(StudentGetsGameProgressOverviewQuery(studentId = student.id))

        result.run {
            levelFourProgress shouldBe StudentGetsGameProgressOverviewQuery.LevelFourProgress(
                firstPayoutState = GameDocumentApprovalState.DENIED,
            )
            levelFiveProgress shouldBe null
        }
    }
}
