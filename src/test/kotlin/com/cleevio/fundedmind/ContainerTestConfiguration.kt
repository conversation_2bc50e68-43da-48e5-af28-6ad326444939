package com.cleevio.fundedmind

import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.DynamicPropertyRegistrar
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.utility.DockerImageName
import kotlin.apply

@TestConfiguration(proxyBeanMethods = false)
class ContainerTestConfiguration {

    @Bean
    fun postgresContainer(): PostgreSQLContainer<*> {
        val postgisImage = DockerImageName
            .parse("postgis/postgis:15-3.5")
            .asCompatibleSubstituteFor("postgres")

        return PostgreSQLContainer(postgisImage)
            .apply {
                withDatabaseName("funded_mind_local")
                withUsername("postgres")
                withPassword("postgres")
            }
    }

    @Bean
    fun postgresPropertyRegistrar(postgresContainer: PostgreSQLContainer<*>): DynamicPropertyRegistrar =
        DynamicPropertyRegistrar { registry ->
            registry.add("spring.datasource.url") { postgresContainer.jdbcUrl }
            registry.add("spring.datasource.username") { postgresContainer.username }
            registry.add("spring.datasource.password") { postgresContainer.password }
            registry.add("spring.datasource.driver-class-name") { "org.postgresql.Driver" }
        }
}
