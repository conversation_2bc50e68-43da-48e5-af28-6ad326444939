package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.highlight.command.PublishHighlightCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.domain.highlight.exception.HighlightMissingPictureException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class PublishHighlightCommandHandlerTest(
    @Autowired private val underTest: PublishHighlightCommandHandler,
    @Autowired private val highlightRepository: HighlightRepository,
) : IntegrationTest() {

    @Test
    fun `should publish highlight`() {
        dataHelper.getHighlight(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
            },
        )

        underTest.handle(PublishHighlightCommand(highlightId = 1.toUUID()))

        highlightRepository.findByIdOrNull(1.toUUID())!!.run {
            published shouldBe true
        }
    }

    @Test
    fun `should throw if highlight is missing desktop photo`() {
        dataHelper.getHighlight(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
            },
        )

        shouldThrow<HighlightMissingPictureException> {
            underTest.handle(PublishHighlightCommand(highlightId = 1.toUUID()))
        }
    }

    @Test
    fun `should throw if highlight is missing mobile photo`() {
        dataHelper.getHighlight(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
            },
        )

        shouldThrow<HighlightMissingPictureException> {
            underTest.handle(PublishHighlightCommand(highlightId = 1.toUUID()))
        }
    }

    @Test
    fun `should throw if highlight is missing both photos`() {
        dataHelper.getHighlight(id = 1.toUUID())

        shouldThrow<HighlightMissingPictureException> {
            underTest.handle(PublishHighlightCommand(highlightId = 1.toUUID()))
        }
    }
}
