package com.cleevio.fundedmind

import com.cleevio.fundedmind.application.module.file.port.out.AppFileStoragePort
import com.cleevio.fundedmind.domain.file.constant.FileType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.io.InputStream

@Service
@ConditionalOnProperty("fundedmind.storage.type", havingValue = "MOCKED_STORAGE")
class AppFileStorageServiceMock : AppFileStoragePort {
    override fun save(
        fileName: String,
        fileType: FileType,
        file: InputStream,
    ): Result<Unit> = Result.success(Unit)

    override fun delete(
        fileName: String,
        fileType: FileType,
    ): Result<Unit> = Result.success(Unit)

    override fun copy(
        sourceFileName: String,
        sourceFileType: FileType,
        destinationFileName: String,
        destinationFileType: FileType,
    ): Result<Unit> = Result.success(Unit)

    override fun getFileUrl(
        fileName: String,
        fileType: FileType,
    ): String = "https://example.com/${fileType.name.lowercase()}/$fileName"
}
