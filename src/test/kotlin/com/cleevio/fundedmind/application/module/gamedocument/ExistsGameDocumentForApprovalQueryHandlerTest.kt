package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.query.ExistsGameDocumentForApprovalQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ExistsGameDocumentForApprovalQueryHandlerTest(
    @Autowired private val underTest: ExistsGameDocumentForApprovalQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return gameDocumentId when game document exists for approval`() {
        // Create a student with AppUser
        val student = dataHelper.getAppUser(id = 201.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document waiting for approval
        val gameDocument = dataHelper.getGameDocument(
            studentId = student.id,
            // state is WAITING
        )

        // Execute the query
        val result = underTest.handle(
            ExistsGameDocumentForApprovalQuery(
                studentId = student.id,
            ),
        )

        // Verify the result
        result.gameDocumentId shouldBe gameDocument.id
        result.gameDocumentExists shouldBe true
    }

    @Test
    fun `should return null gameDocumentId when no game document exists for approval`() {
        // Create a student with AppUser
        val student = dataHelper.getAppUser(id = 202.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Execute the query
        val result = underTest.handle(
            ExistsGameDocumentForApprovalQuery(
                studentId = student.id,
            ),
        )

        // Verify the result
        result.gameDocumentId shouldBe null
        result.gameDocumentExists shouldBe false
    }

    @Test
    fun `should return null gameDocumentId when game document exists but in APPROVED state`() {
        // Create a student with AppUser
        val student = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document in APPROVED state
        dataHelper.getGameDocument(
            studentId = student.id,
            entityModifier = { it.approveAwaiting() },
        )

        // Execute the query
        val result = underTest.handle(
            ExistsGameDocumentForApprovalQuery(
                studentId = student.id,
            ),
        )

        // Verify the result
        result.gameDocumentId shouldBe null
        result.gameDocumentExists shouldBe false
    }

    @Test
    fun `should return null gameDocumentId when game document exists but in DENIED state`() {
        // Create a student with AppUser
        val student = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document in APPROVED state
        dataHelper.getGameDocument(
            studentId = student.id,
            entityModifier = { it.denyAwaiting("denied") },
        )

        // Execute the query
        val result = underTest.handle(
            ExistsGameDocumentForApprovalQuery(
                studentId = student.id,
            ),
        )

        // Verify the result
        result.gameDocumentId shouldBe null
        result.gameDocumentExists shouldBe false
    }
}
