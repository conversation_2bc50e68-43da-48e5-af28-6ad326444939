package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.command.PublishReferralCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import com.cleevio.fundedmind.domain.referral.exception.ReferralMissingPictureException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class PublishReferralCommandHandlerTest(
    @Autowired private val underTest: PublishReferralCommandHandler,
    @Autowired private val referralRepository: ReferralRepository,
) : IntegrationTest() {

    @Test
    fun `should publish referral`() {
        dataHelper.getReferral(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
            },
        )

        underTest.handle(PublishReferralCommand(referralId = 1.toUUID()))

        referralRepository.findByIdOrNull(1.toUUID())!!.run {
            published shouldBe true
        }
    }

    @Test
    fun `should throw if referral is missing desktop photo`() {
        dataHelper.getReferral(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageMobile(dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
            },
        )

        shouldThrow<ReferralMissingPictureException> {
            underTest.handle(PublishReferralCommand(referralId = 1.toUUID()))
        }
    }

    @Test
    fun `should throw if referral is missing mobile photo`() {
        dataHelper.getReferral(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
            },
        )

        shouldThrow<ReferralMissingPictureException> {
            underTest.handle(PublishReferralCommand(referralId = 1.toUUID()))
        }
    }

    @Test
    fun `should throw if referral is missing both photos`() {
        dataHelper.getReferral(id = 1.toUUID())

        shouldThrow<ReferralMissingPictureException> {
            underTest.handle(PublishReferralCommand(referralId = 1.toUUID()))
        }
    }
}
