spring:
  mail:
    host: localhost
    username: username
    password: secret
    port: 3025
    properties:
      mail:
        debug: false
        smtp:
          from: fundedmind-test
          debug: false
          auth: true
          starttls:
            enable: true

logging:
  level:
    root: INFO
    org.hibernate.SQL: INFO
    org.hibernate.orm.jdbc.bind: INFO
    org.hibernate.stat: INFO
    org.hibernate.SQL_SLOW: INFO
    org.hibernate.cache: INFO
    org.jooq: INFO
    org.jooq.tools.LoggerListener: INFO

springdoc:
  swagger-server:
  pre-loading-enabled: false
  show-actuator: false

management:
  metrics:
    enabled: false
  observations:
    enabled: false

sentry:
  enabled: false
  dsn:

firebase:
  credentials: '{}'

fundedmind:
  storage:
    type: MOCKED_STORAGE
  security:
    api-key: fundedmind-api-key
    webhook:
      stripe:
        secret-key: "stripe-webhook-secret-key"
  public-data:
    enable-caching: false

integration:
  stripe:
    api-key: stripe-api-key-test
    product:
      masterclass: prod_MASTERCLASS
      exclusive: prod_EXCLUSIVE
      discord: prod_DISCORD
