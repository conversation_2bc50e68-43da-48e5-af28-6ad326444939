package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fundedmind.application.module.gamedocument.query.SearchGameDocumentsQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate

class SearchGameDocumentsQueryHandlerTest(
    @Autowired private val underTest: SearchGameDocumentsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should search for game documents - verify mappings`() {
        // given
        val student1 = dataHelper.getAppUser(
            id = 1.toUUID(),
            email = "<EMAIL>",
            userRole = UserRole.STUDENT,
        ).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                gameLevel = GameLevel.FOUR,
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = {
                    it.changeProfilePicture(
                        dataHelper.getImage(
                            type = FileType.STUDENT_PROFILE_PICTURE,
                            originalFileUrl = "profile-url",
                            compressedFileUrl = "profile-url-comp",
                            blurHash = "123",
                        ).id,
                    )
                },
            )
        }

        val doc1 = dataHelper.getGameDocument(
            id = 1.toUUID(),
            createdTimestamp = "2025-01-01T10:00:00Z".toInstant(),
            studentId = student1.id,
            type = GameDocumentType.BACKTESTING,
            issuingCompany = IssuingCompany.ALPHA_FUTURES,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
            payoutDate = LocalDate.of(2025, 6, 20),
            truthScore = 95,
            scoreMessage = "Great achievement!",
            entityModifier = {
                it.changeGameDocumentFile(
                    fileId = dataHelper.getDocument(id = 2.toUUID(), type = FileType.GAME_DOCUMENT).id,
                )
                it.approveAwaiting()
            },
        )

        val doc2 = dataHelper.getGameDocument(
            id = 2.toUUID(),
            createdTimestamp = "2025-01-02T10:00:00Z".toInstant(),
            studentId = student1.id,
            type = GameDocumentType.CERTIFICATE,
            issuingCompany = IssuingCompany.TOPSTEP,
            payoutAmount = null,
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.THREE,
            payoutDate = LocalDate.of(2025, 6, 25),
            truthScore = 95,
            scoreMessage = "Great achievement!",
            entityModifier = {
                it.changeGameDocumentFile(
                    fileId = dataHelper.getDocument(id = 3.toUUID(), type = FileType.GAME_DOCUMENT).id,
                )
                it.approveAwaiting()
            },
        )

        val doc3 = dataHelper.getGameDocument(
            id = 3.toUUID(),
            createdTimestamp = "2025-01-03T10:00:00Z".toInstant(),
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FTMO,
            payoutAmount = BigDecimal("1000.00"),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.FOUR,
            payoutDate = LocalDate.of(2025, 6, 30),
            truthScore = 95,
            scoreMessage = "Great achievement!",
            entityModifier = {
                it.changeGameDocumentFile(
                    fileId = dataHelper.getDocument(id = 1.toUUID(), type = FileType.GAME_DOCUMENT).id,
                )
            },
        )

        // when
        val slice = underTest.handle(
            SearchGameDocumentsQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchGameDocumentsQuery.Filter(
                    studentId = null,
                    state = null,
                ),
            ),
        )

        // then
        slice.content shouldHaveSize 3
        slice.hasMore shouldBe false
        slice.lastId shouldBe 3.toUUID()
        assertSoftly {
            slice.content.first { it.gameDocumentId == 1.toUUID() }.run {
                gameDocumentId shouldBe 1.toUUID()
                createdAt shouldBeAbout "2025-01-01T10:00:00Z".toInstant()
                type shouldBe GameDocumentType.BACKTESTING
                issuingCompany shouldBe IssuingCompany.ALPHA_FUTURES
                payoutAmount shouldBe null
                previousLevel shouldBe GameLevel.ONE
                reachedLevel shouldBe GameLevel.TWO
                payoutDate shouldBe LocalDate.of(2025, 6, 20)
                gameDocumentFile shouldNotBe null
                gameDocumentFile!!.run {
                    documentId shouldBe 2.toUUID()
                }
                truthScore shouldBe 95
                state shouldBe GameDocumentApprovalState.APPROVED
                denyMessage shouldBe null
                student.run {
                    studentId shouldBe student1.id
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    email shouldBe "<EMAIL>"
                    profilePicture shouldNotBe null
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "profile-url"
                        imageCompressedUrl shouldBe "profile-url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    currentGameLevel shouldBe GameLevel.FOUR
                }
            }
            slice.content.first { it.gameDocumentId == 2.toUUID() }.run {
                gameDocumentId shouldBe 2.toUUID()
                createdAt shouldBeAbout "2025-01-02T10:00:00Z".toInstant()
                type shouldBe GameDocumentType.CERTIFICATE
                issuingCompany shouldBe IssuingCompany.TOPSTEP
                payoutAmount shouldBe null
                previousLevel shouldBe GameLevel.TWO
                reachedLevel shouldBe GameLevel.THREE
                payoutDate shouldBe LocalDate.of(2025, 6, 25)
                gameDocumentFile shouldNotBe null
                gameDocumentFile!!.run {
                    documentId shouldBe 3.toUUID()
                }
                truthScore shouldBe 95
                state shouldBe GameDocumentApprovalState.APPROVED
                denyMessage shouldBe null
                student.run {
                    studentId shouldBe student1.id
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    email shouldBe "<EMAIL>"
                    profilePicture shouldNotBe null
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "profile-url"
                        imageCompressedUrl shouldBe "profile-url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    currentGameLevel shouldBe GameLevel.FOUR
                }
            }
            slice.content.first { it.gameDocumentId == 3.toUUID() }.run {
                gameDocumentId shouldBe 3.toUUID()
                createdAt shouldBeAbout "2025-01-03T10:00:00Z".toInstant()
                type shouldBe GameDocumentType.PAYOUT
                issuingCompany shouldBe IssuingCompany.FTMO
                payoutAmount!! shouldBeEqualComparingTo BigDecimal("1000.00")
                previousLevel shouldBe GameLevel.THREE
                reachedLevel shouldBe GameLevel.FOUR
                payoutDate shouldBe LocalDate.of(2025, 6, 30)
                gameDocumentFile shouldNotBe null
                gameDocumentFile!!.run {
                    documentId shouldBe 1.toUUID()
                }
                truthScore shouldBe 95
                state shouldBe GameDocumentApprovalState.WAITING
                denyMessage shouldBe null
                student.run {
                    studentId shouldBe student1.id
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    email shouldBe "<EMAIL>"
                    profilePicture shouldNotBe null
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "profile-url"
                        imageCompressedUrl shouldBe "profile-url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    currentGameLevel shouldBe GameLevel.FOUR
                }
            }
        }
    }

    @Test
    fun `should filter game documents by student id`() {
        // given
        val student1 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val student2 = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = student1.id,
        )

        dataHelper.getGameDocument(
            id = 3.toUUID(),
            studentId = student2.id,
        )

        // when
        val slice = underTest.handle(
            SearchGameDocumentsQuery(
                infiniteScroll = InfiniteScrollDesc.Identifier(),
                filter = SearchGameDocumentsQuery.Filter(
                    studentId = student1.id,
                    state = null,
                ),
            ),
        )

        // then
        slice.content shouldHaveSize 1
        slice.hasMore shouldBe false
        slice.content.single().gameDocumentId shouldBe 2.toUUID()
    }

    @Test
    fun `should filter game documents by approval state`() {
        // given
        val student = dataHelper.getAppUser(id = 4.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        dataHelper.getGameDocument(
            id = 4.toUUID(),
            studentId = student.id,
        ) // state remains WAITING

        dataHelper.getGameDocument(
            id = 5.toUUID(),
            studentId = student.id,
            entityModifier = {
                it.approveAwaiting()
            },
        )

        dataHelper.getGameDocument(
            id = 6.toUUID(),
            studentId = student.id,
            entityModifier = {
                it.denyAwaiting("Invalid document")
            },
        )

        // when - filter by WAITING state
        val waitingSlice = underTest.handle(
            SearchGameDocumentsQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchGameDocumentsQuery.Filter(
                    studentId = null,
                    state = GameDocumentApprovalState.WAITING,
                ),
            ),
        )

        // then
        waitingSlice.content shouldHaveSize 1
        waitingSlice.content.single().gameDocumentId shouldBe 4.toUUID()

        // when - filter by APPROVED state
        val approvedSlice = underTest.handle(
            SearchGameDocumentsQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchGameDocumentsQuery.Filter(
                    studentId = null,
                    state = GameDocumentApprovalState.APPROVED,
                ),
            ),
        )

        // then
        approvedSlice.content shouldHaveSize 1
        approvedSlice.content.single().gameDocumentId shouldBe 5.toUUID()

        // when - filter by DENIED state
        val deniedSlice = underTest.handle(
            SearchGameDocumentsQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchGameDocumentsQuery.Filter(
                    studentId = null,
                    state = GameDocumentApprovalState.DENIED,
                ),
            ),
        )

        // then
        deniedSlice.content shouldHaveSize 1
        deniedSlice.content.single().gameDocumentId shouldBe 6.toUUID()
    }
}
