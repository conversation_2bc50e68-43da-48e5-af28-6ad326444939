package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.command.ReorderLessonsInCourseModuleCommand
import com.cleevio.fundedmind.application.module.lesson.command.ReorderLessonsInCourseModuleCommand.LessonOrderingInput
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.exception.ActiveLessonsMismatchException
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.domain.lesson.exception.LessonOrderCannotBeNegativeException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
class ReorderLessonsInCourseModuleCommandHandlerTest(
    @Autowired private val underTest: ReorderLessonsInCourseModuleCommandHandler,
    @Autowired private val lessonRepository: LessonRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder lessons`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id, listingOrder = 1)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule.id, listingOrder = 2)
        dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id, listingOrder = 3)

        // when
        underTest.handle(
            ReorderLessonsInCourseModuleCommand(
                courseId = course.id,
                courseModuleId = courseModule.id,
                lessonOrderings = listOf(
                    LessonOrderingInput(lessonId = 1.toUUID(), newListingOrder = 2),
                    LessonOrderingInput(lessonId = 2.toUUID(), newListingOrder = 3),
                    LessonOrderingInput(lessonId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val lessons = lessonRepository.findAll()
        lessons shouldHaveSize 3
        lessons.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        lessons.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        lessons.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should reorder lessons only in the given course module`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule1 = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val courseModule2 = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule1.id, listingOrder = 1)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule1.id, listingOrder = 2)
        dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule2.id, listingOrder = 9)

        // when
        underTest.handle(
            ReorderLessonsInCourseModuleCommand(
                courseId = course.id,
                courseModuleId = courseModule1.id,
                lessonOrderings = listOf(
                    LessonOrderingInput(lessonId = 1.toUUID(), newListingOrder = 2),
                    LessonOrderingInput(lessonId = 2.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val lessons = lessonRepository.findAll()
        lessons shouldHaveSize 3
        lessons.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        lessons.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        lessons.first { it.id == 3.toUUID() }.listingOrder shouldBe 9
    }

    @Test
    fun `should throw when lesson count does not match`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id, listingOrder = 1)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule.id, listingOrder = 2)

        // when/then
        shouldThrow<ActiveLessonsMismatchException> {
            underTest.handle(
                ReorderLessonsInCourseModuleCommand(
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonOrderings = listOf(
                        LessonOrderingInput(lessonId = 1.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw lesson count by module match but module is of different course`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        val courseModule1 = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        val courseModule2 = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id)

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule1.id, listingOrder = 1)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule2.id, listingOrder = 2)

        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                ReorderLessonsInCourseModuleCommand(
                    courseId = course.id,
                    courseModuleId = courseModule1.id,
                    // count of module orderings is 1 and matches amount of lessons in module 2
                    // however provided lesson is not in module 2
                    lessonOrderings = listOf(
                        LessonOrderingInput(lessonId = 2.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should reorder courses even if display order is not unique`() {
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id, listingOrder = 1)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule.id, listingOrder = 2)
        dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id, listingOrder = 3)

        underTest.handle(
            ReorderLessonsInCourseModuleCommand(
                courseId = course.id,
                courseModuleId = courseModule.id,
                lessonOrderings = listOf(
                    LessonOrderingInput(lessonId = 1.toUUID(), newListingOrder = 1),
                    LessonOrderingInput(lessonId = 2.toUUID(), newListingOrder = 1),
                    LessonOrderingInput(lessonId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        val lessons = lessonRepository.findAll()
        lessons shouldHaveSize 3
        lessons.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        lessons.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        lessons.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of courses - course is missing`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id, listingOrder = 1)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = courseModule.id, listingOrder = 2)
        dataHelper.getLesson(id = 3.toUUID(), courseModuleId = courseModule.id, listingOrder = 3)

        shouldThrow<ActiveLessonsMismatchException> {
            underTest.handle(
                ReorderLessonsInCourseModuleCommand(
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonOrderings = listOf(
                        LessonOrderingInput(lessonId = 1.toUUID(), newListingOrder = 2),
                        LessonOrderingInput(lessonId = 3.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if listing order is negative`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)
        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = courseModule.id, listingOrder = 1)

        // when/then
        shouldThrow<LessonOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderLessonsInCourseModuleCommand(
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                    lessonOrderings = listOf(
                        LessonOrderingInput(lessonId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}
