package com.cleevio.fundedmind.application.module.lesson

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.lesson.query.ListLessonsInCourseModuleQuery
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotRelatedToCourseException
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentValue
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ListLessonsInCourseModuleQueryHandlerTest(
    @Autowired private val underTest: ListLessonsInCourseModuleQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list lessons in course module - verify mappings`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            listingOrder = 1,
        )

        dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 1,
            title = "First Lesson",
            durationInSeconds = 3600,
            videoUrl = "video-url",
            thumbnailUrl = "thumbnail-url",
            thumbnailAnimationUrl = "thumbnail-animation-url",
            attachments = listOf(
                LessonAttachmentValue(
                    name = "attachment1",
                    type = LessonAttachmentType.PDF,
                    displayOrder = 1,
                ),
                LessonAttachmentValue(
                    name = "attachment2",
                    type = LessonAttachmentType.XLSX,
                    displayOrder = 2,
                ),
            ),
        )

        // when
        val result = underTest.handle(
            ListLessonsInCourseModuleQuery(
                filter = ListLessonsInCourseModuleQuery.Filter(
                    searchString = null,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            ),
        )

        // then
        result.data shouldHaveSize 1
        result.data.first().run {
            lessonId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            videoUrl shouldBe "video-url"
            title shouldBe "First Lesson"
            durationInSeconds shouldBe 3600
            thumbnailUrl shouldBe "thumbnail-url"
            thumbnailAnimationUrl shouldBe "thumbnail-animation-url"
            attachmentCount shouldBe 2
        }
    }

    @Test
    fun `should return zero attachment count when lesson has no attachments`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            listingOrder = 1,
        )

        dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            attachments = emptyList(),
        )

        // when
        val result = underTest.handle(
            ListLessonsInCourseModuleQuery(
                filter = ListLessonsInCourseModuleQuery.Filter(
                    searchString = null,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            ),
        )

        // then
        result.data.first().attachmentCount shouldBe 0
    }

    @Test
    fun `should list lessons ordered by listing order`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
        )

        dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 2,
        )
        dataHelper.getLesson(
            id = 2.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 1,
        )
        dataHelper.getLesson(
            id = 3.toUUID(),
            courseModuleId = courseModule.id,
            listingOrder = 3,
        )

        // when
        val result = underTest.handle(
            ListLessonsInCourseModuleQuery(
                filter = ListLessonsInCourseModuleQuery.Filter(
                    searchString = null,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            ),
        )

        // then
        result.data.map { it.lessonId } shouldBe listOf(2.toUUID(), 1.toUUID(), 3.toUUID())
    }

    @Test
    fun `should list lessons only from given course module`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        val courseModule1 = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
        )
        val differentModule = dataHelper.getCourseModule(
            id = 2.toUUID(),
            courseId = course.id,
        )

        dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule1.id,
        )
        dataHelper.getLesson(
            id = 2.toUUID(),
            courseModuleId = courseModule1.id,
        )
        dataHelper.getLesson(
            id = 3.toUUID(),
            courseModuleId = differentModule.id,
        )

        // when
        val result = underTest.handle(
            ListLessonsInCourseModuleQuery(
                filter = ListLessonsInCourseModuleQuery.Filter(
                    searchString = null,
                    courseId = course.id,
                    courseModuleId = courseModule1.id,
                ),
            ),
        )

        // then
        result.data.map { it.lessonId } shouldBe listOf(1.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list lessons without deleted ones`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
        )

        dataHelper.getLesson(
            id = 1.toUUID(),
            courseModuleId = courseModule.id,
        )
        dataHelper.getLesson(
            id = 2.toUUID(),
            courseModuleId = courseModule.id,
            entityModifier = { it.softDelete() },
        )

        // when
        val result = underTest.handle(
            ListLessonsInCourseModuleQuery(
                filter = ListLessonsInCourseModuleQuery.Filter(
                    searchString = null,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            ),
        )

        // then
        result.data.map { it.lessonId } shouldBe listOf(1.toUUID())
    }

    @Test
    fun `should throw if course module is not related to course`() {
        val course1 = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        val course2 = dataHelper.getCourse(id = 2.toUUID(), traderId = dataHelper.getTrader(2.toUUID()).id)
        val courseModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course1.id)

        shouldThrow<CourseModuleNotRelatedToCourseException> {
            underTest.handle(
                ListLessonsInCourseModuleQuery(
                    filter = ListLessonsInCourseModuleQuery.Filter(
                        searchString = null,
                        courseId = course2.id,
                        courseModuleId = courseModule.id,
                    ),
                ),
            )
        }
    }
}
