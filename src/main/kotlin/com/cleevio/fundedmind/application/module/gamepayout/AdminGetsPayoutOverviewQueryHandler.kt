package com.cleevio.fundedmind.application.module.gamepayout

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.gamepayout.port.GamePayoutOverviewPort
import com.cleevio.fundedmind.application.module.gamepayout.query.AdminGetsPayoutOverviewQuery
import org.springframework.stereotype.Component

@Component
class AdminGetsPayoutOverviewQueryHandler(
    private val gamePayoutOverviewPort: GamePayoutOverviewPort,
) : QueryHandler<AdminGetsPayoutOverviewQuery.Result, AdminGetsPayoutOverviewQuery> {

    override val query = AdminGetsPayoutOverviewQuery::class

    override fun handle(query: AdminGetsPayoutOverviewQuery): AdminGetsPayoutOverviewQuery.Result {
        val payoutOverview = gamePayoutOverviewPort.getGamePayoutOverview(query.year)

        return AdminGetsPayoutOverviewQuery.Result(
            offsetTotalPayout = payoutOverview.offsetTotalPayout,
            realTotalPayout = payoutOverview.realTotalPayout,
            approvedPayouts = payoutOverview.approvedPayouts,
            year = payoutOverview.year,
        )
    }
}
