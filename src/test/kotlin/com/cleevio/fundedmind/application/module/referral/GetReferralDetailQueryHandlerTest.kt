package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.query.GetReferralDetailQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetReferralDetailQueryHandlerTest(
    @Autowired private val underTest: GetReferralDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get referral detail - verify mappings`() {
        // given
        dataHelper.getReferral(
            id = 1.toUUID(),
            listingOrder = 5,
            title = "Referral 1",
            description = "Referral description",
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            linkUrl = "referral-url",
            rewardCouponCode = "REWARD123",
            entityModifier = {
                it.changeImageDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.REFERRAL_DESKTOP_PHOTO,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeImageMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.REFERRAL_MOBILE_PHOTO,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            GetReferralDetailQuery(
                referralId = 1.toUUID(),
            ),
        )

        // then
        result.run {
            referralId shouldBe 1.toUUID()
            listingOrder shouldBe 5
            published shouldBe false
            imageDesktop shouldNotBe null
            imageDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            imageMobile shouldNotBe null
            imageMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            title shouldBe "Referral 1"
            description shouldBe "Referral description"
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe "referral-url"
            rewardCouponCode shouldBe "REWARD123"
        }
    }

    @Test
    fun `should get referral detail with null optional fields`() {
        // given
        dataHelper.getReferral(
            id = 2.toUUID(),
            listingOrder = 10,
            title = null,
            description = null,
            visibleToTiers = listOf(),
            visibleToDiscordUsers = false,
            linkUrl = null,
            rewardCouponCode = null,
        )

        // when
        val result = underTest.handle(
            GetReferralDetailQuery(
                referralId = 2.toUUID(),
            ),
        )

        // then
        result.run {
            referralId shouldBe 2.toUUID()
            listingOrder shouldBe 10
            published shouldBe false
            imageDesktop shouldBe null
            imageMobile shouldBe null
            title shouldBe null
            description shouldBe null
            visibleToTiers shouldBe emptyList()
            visibleToDiscordUsers shouldBe false
            linkUrl shouldBe null
            rewardCouponCode shouldBe null
        }
    }
}
