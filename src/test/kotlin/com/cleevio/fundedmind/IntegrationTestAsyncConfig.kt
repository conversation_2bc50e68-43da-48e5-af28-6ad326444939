package com.cleevio.fundedmind

import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.core.task.SimpleAsyncTaskExecutor
import org.springframework.core.task.TaskExecutor
import org.springframework.scheduling.annotation.AsyncConfigurer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.stereotype.Component
import java.lang.reflect.Method

@EnableAsync
@TestConfiguration
class IntegrationTestAsyncConfig : AsyncConfigurer {

    @Bean
    @Primary
    override fun getAsyncExecutor() = SynchronousAsyncExecutor()

    override fun getAsyncUncaughtExceptionHandler(): AsyncUncaughtExceptionHandler =
        CountingAsyncUncaughtExceptionHandler
}

@Component
object CountingAsyncUncaughtExceptionHandler : SimpleAsyncUncaughtExceptionHandler() {

    private var exceptionsInAsyncThreads = 0

    fun reset() {
        exceptionsInAsyncThreads = 0
    }

    fun assertNoExceptionsThrownInAsyncThreads() {
        check(exceptionsInAsyncThreads == 0) {
            "Main test thread was successful, however at least one exception occurred in asynchronously executed tasks!"
        }
    }

    private fun recordException() {
        exceptionsInAsyncThreads += 1
    }

    override fun handleUncaughtException(
        ex: Throwable,
        method: Method,
        vararg params: Any?,
    ) {
        recordException()
        throw ex
    }
}

/**
 * Run async tasks on different thread to enable transactional behaviour, however waits immediately
 * for async task execution end.
 */
class SynchronousAsyncExecutor : TaskExecutor {

    private val executor = SimpleAsyncTaskExecutor()

    override fun execute(task: Runnable) {
        executor.submit(task).get()
    }
}
