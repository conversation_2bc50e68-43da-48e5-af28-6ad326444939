package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.module.product.command.UpdateProductCommand
import com.cleevio.fundedmind.application.module.product.exception.ProductAlreadyExistsException
import com.cleevio.fundedmind.application.module.product.exception.ProductCannotBeUpdatedException
import com.cleevio.fundedmind.application.module.product.exception.ProductNotFoundException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderNotFoundException
import com.cleevio.fundedmind.domain.product.ProductRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class UpdateProductCommandHandlerTest(
    @Autowired private val underTest: UpdateProductCommandHandler,
    @Autowired private val productRepository: ProductRepository,
) : IntegrationTest() {

    @Test
    fun `should update product - update everything`() {
        every { existsExternalProductPort.existsByIdentifier("prod_2") } returns true

        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getTrader(id = 2.toUUID())

        // given
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            name = "product",
            sessionsCount = 5,
            description = "description",
            altDescription = "alt description",
            stripeIdentifier = "prod_1",
            validityInDays = null,
        )

        // when
        underTest.handle(
            defaultCommand(
                productId = 1.toUUID(),
                traderId = 2.toUUID(),
                name = "new name",
                sessionsCount = 10,
                stripeIdentifier = "prod_2",
                description = "new description",
                altDescription = "new alt description",
                validityInDays = 10,
            ),
        )

        // then
        productRepository.findByIdOrNull(1.toUUID())!!.run {
            name shouldBe "new name"
            sessionsCount shouldBe 10
            stripeIdentifier shouldBe "prod_2"
            description shouldBe "new description"
            altDescription shouldBe "new alt description"
            validityInDays shouldBe 10
            saleable shouldBe true
            traderId shouldBe 2.toUUID()
        }

        verify { existsExternalProductPort.existsByIdentifier("prod_2") }
    }

    @Test
    fun `should throw if product does not exist`() {
        dataHelper.getTrader(id = 1.toUUID())

        shouldThrow<ProductNotFoundException> {
            underTest.handle(
                defaultCommand(productId = 999.toUUID(), traderId = 1.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if trader does not exist`() {
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
        )

        shouldThrow<TraderNotFoundException> {
            underTest.handle(
                defaultCommand(productId = 1.toUUID(), traderId = 999.toUUID()),
            )
        }
    }

    @Test
    fun `should throw if external product is not verified`() {
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            stripeIdentifier = "old_identifier",
        )

        every { existsExternalProductPort.existsByIdentifier("new_identifier") } returns false

        shouldThrow<ProductNotFoundException> {
            underTest.handle(
                defaultCommand(
                    productId = 1.toUUID(),
                    traderId = 1.toUUID(),
                    stripeIdentifier = "new_identifier",
                ),
            )
        }

        verify { existsExternalProductPort.existsByIdentifier("new_identifier") }
    }

    @Test
    fun `should throw if product with stripe identifier already exists`() {
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            stripeIdentifier = "old",
        )

        dataHelper.getProduct(
            id = 2.toUUID(),
            traderId = dataHelper.getTrader(id = 2.toUUID()).id,
            stripeIdentifier = "existing",
        )

        shouldThrow<ProductAlreadyExistsException> {
            underTest.handle(
                defaultCommand(
                    productId = 1.toUUID(),
                    traderId = 1.toUUID(),
                    stripeIdentifier = "existing",
                ),
            )
        }
    }

    @Test
    fun `should not check stripe identifier if it is not to be changed`() {
        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            stripeIdentifier = "prod_123",
            name = "old name",
        )

        underTest.handle(
            defaultCommand(
                productId = 1.toUUID(),
                traderId = 1.toUUID(),
                name = "new name",
                stripeIdentifier = "prod_123", // stays the same
            ),
        )

        productRepository.findByIdOrNull(1.toUUID())!!.run {
            name shouldBe "new name"
            stripeIdentifier shouldBe "prod_123"
        }

        verify(exactly = 0) { existsExternalProductPort.existsByIdentifier("prod_123") }
    }

    @Test
    fun `should throw if update product changes trader and product was already sold as mentoring`() {
        val trader1 = dataHelper.getTrader(id = 1.toUUID())
        val trader2 = dataHelper.getTrader(id = 2.toUUID())

        val product = dataHelper.getProduct(id = 1.toUUID(), traderId = trader1.id, stripeIdentifier = "prod_123")

        dataHelper.getMentoring(productId = product.id, studentId = dataHelper.getStudent().id)

        shouldThrow<ProductCannotBeUpdatedException> {
            underTest.handle(
                defaultCommand(
                    productId = product.id,
                    traderId = trader2.id, // change of trader
                ),
            )
        }
    }

    @Test
    fun `should throw if update product changes stripe identifier and product was already sold as mentoring`() {
        every { existsExternalProductPort.existsByIdentifier("prod_456") } returns true

        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            stripeIdentifier = "prod_123",
        )

        dataHelper.getMentoring(productId = product.id, studentId = dataHelper.getStudent().id)

        shouldThrow<ProductCannotBeUpdatedException> {
            underTest.handle(
                defaultCommand(
                    productId = product.id,
                    traderId = trader.id,
                    stripeIdentifier = "prod_456", // change of stripe identifier
                ),
            )
        }

        verify { existsExternalProductPort.existsByIdentifier("prod_456") }
    }

    private fun defaultCommand(
        productId: UUID,
        traderId: UUID,
        name: String = "5x Mentoring 1-on-1",
        stripeIdentifier: StripeProductId = "prod_123",
        description: String = "Dope Mentoring",
        altDescription: String = "Product for mentoring",
        sessionsCount: Int = 5,
        validityInDays: Int? = null,
    ) = UpdateProductCommand(
        productId = productId,
        name = name,
        stripeIdentifier = stripeIdentifier,
        description = description,
        altDescription = altDescription,
        sessionsCount = sessionsCount,
        traderId = traderId,
        validityInDays = validityInDays,
    )
}
