package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.command.CreateNewReferralCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateNewReferralCommandHandlerTest(
    @Autowired private val underTest: CreateNewReferralCommandHandler,
    @Autowired private val referralRepository: ReferralRepository,
) : IntegrationTest() {

    @Test
    fun `should create new referral with provided parameters`() {
        // given
        val command = CreateNewReferralCommand(
            title = "Test Referral",
            description = "Test Description",
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            linkUrl = "https://example.com",
            rewardCouponCode = "TEST123",
        )

        // when
        val result = underTest.handle(command)

        // then
        referralRepository.getReferenceById(result.id).run {
            title shouldBe "Test Referral"
            description shouldBe "Test Description"
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe "https://example.com"
            rewardCouponCode shouldBe "TEST123"
            listingOrder shouldBe 1 // Since it's the first referral, order should be 1
            published shouldBe false // New referrals should be unpublished by default
        }
    }

    @Test
    fun `should create new referral with correct listing order when other referrals exist`() {
        // given
        // Create a first referral
        val firstCommand = CreateNewReferralCommand(
            title = "First Referral",
            description = "First Description",
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = false,
            linkUrl = null,
            rewardCouponCode = null,
        )
        underTest.handle(firstCommand)

        // Create a second referral
        val secondCommand = CreateNewReferralCommand(
            title = "Second Referral",
            description = "Second Description",
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            linkUrl = "https://example.com/second",
            rewardCouponCode = "SECOND123",
        )

        // when
        val result = underTest.handle(secondCommand)

        // then
        referralRepository.getReferenceById(result.id).run {
            title shouldBe "Second Referral"
            listingOrder shouldBe 2 // Should be 2 since it's the second referral
        }
    }

    @Test
    fun `should create new referral with null optional fields`() {
        // given
        val command = CreateNewReferralCommand(
            title = null,
            description = null,
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = false,
            linkUrl = null,
            rewardCouponCode = null,
        )

        // when
        val result = underTest.handle(command)

        // then
        referralRepository.getReferenceById(result.id).run {
            title shouldBe null
            description shouldBe null
            visibleToTiers shouldBe listOf(StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe false
            linkUrl shouldBe null
            rewardCouponCode shouldBe null
        }
    }
}
