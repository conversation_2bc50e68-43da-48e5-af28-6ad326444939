package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.command.DeleteReferralCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class DeleteReferralCommandHandlerTest(
    @Autowired private val underTest: DeleteReferralCommandHandler,
    @Autowired private val referralRepository: ReferralRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete referral`() {
        // given
        // Create a referral first
        val createdReferral = dataHelper.getReferral(
            id = 0.toUUID(),
            title = "Test Referral",
            description = "Test Description",
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = false,
            linkUrl = "https://example.com",
            rewardCouponCode = "TEST123",
        )

        // when
        underTest.handle(
            DeleteReferralCommand(
                referralId = 0.toUUID(),
            ),
        )

        // then
        referralRepository.getReferenceById(createdReferral.id).run {
            this.isDeleted shouldBe true
        }
    }
}
