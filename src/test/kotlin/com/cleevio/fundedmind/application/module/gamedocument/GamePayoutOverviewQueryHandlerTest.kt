package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamepayout.GamePayoutOverviewQueryHandler
import com.cleevio.fundedmind.application.module.gamepayout.query.GamePayoutOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Year

class GamePayoutOverviewQueryHandlerTest(
    @Autowired private val underTest: GamePayoutOverviewQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return payout overview with approved payouts and latest student payouts`() {
        // Create students
        val student1 = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FIVE,
                levelVisibility = LevelVisibility.DISABLED,
                entityModifier = { student ->
                    student.changeProfilePicture(
                        dataHelper.getImage(
                            type = FileType.STUDENT_PROFILE_PICTURE,
                            originalFileUrl = "profile-url",
                            compressedFileUrl = "profile-url-comp",
                            blurHash = "123",
                        ).id,
                    )
                },
            )
        }
        val student2 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "Jane",
                studentTier = StudentTier.EXCLUSIVE,
                gameLevel = GameLevel.SIX,
                levelVisibility = LevelVisibility.DISABLED,
                networkingVisibility = NetworkingVisibility.DISABLED,
            )
        }
        val student3 = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "Bob",
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.SEVEN,
                levelVisibility = LevelVisibility.ENABLED,
                networkingVisibility = NetworkingVisibility.ENABLED,
            )
        }

        // Create approved payout documents for the year
        val approvedPayout1 = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 6, 15),
            entityModifier = { it.approveAwaiting(now = "2025-06-15T10:00:00Z".toInstant()) },
        )

        val approvedPayout2 = dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("500.50"),
            payoutDate = LocalDate.of(2025, 8, 20),
            entityModifier = { it.approveAwaiting(now = "2025-08-20T10:00:00Z".toInstant()) },
        )

        val approvedPayout3 = dataHelper.getGameDocument(
            studentId = student3.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("750.25"),
            payoutDate = LocalDate.of(2025, 9, 5),
            entityModifier = { it.approveAwaiting(now = "2025-09-05T10:00:00Z".toInstant()) },
        )

        // Create a waiting payout (should not be counted)
        val waitingPayout = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("300.00"),
            payoutDate = LocalDate.of(2025, 9, 10),
        ) // state remains WAITING

        // Create a denied payout (should not be counted)
        val deniedPayout = dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("400.00"),
            payoutDate = LocalDate.of(2025, 9, 15),
            entityModifier = { it.denyAwaiting("Rejected") },
        )

        // Create payout settings for the year
        dataHelper.getGamePayoutSettings(
            year = 2025,
            payoutGoal = BigDecimal("5000.00"),
            payoutOffset = BigDecimal("250.75"),
        )

        val result = underTest.handle(
            GamePayoutOverviewQuery(
                userId = 1.toUUID(),
                limit = 5,
                year = Year.of(2025),
            ),
        )

        result.run {
            currentTotalPayout shouldBeEqualComparingTo BigDecimal("2501.50") // 1000.00 + 500.50 + 750.25 + 250.75
            currentPayoutGoal shouldBeEqualComparingTo BigDecimal("5000.00")
            year shouldBe 2025
            latestStudentPayouts.size shouldBe 3

            latestStudentPayouts[0].run {
                studentId shouldBe student3.id
                approvedAt shouldBeAbout "2025-09-05T10:00:00Z".toInstant() // 5.9.2025 10:00
                payoutAmount shouldBeEqualComparingTo BigDecimal("750.25")
                firstName shouldBe "Bob"
                gameLevel shouldBe GameLevel.SEVEN
                profilePicture shouldBe null
                networkingVisibility shouldBe NetworkingVisibility.ENABLED
            }

            latestStudentPayouts[1].run {
                studentId shouldBe student2.id
                approvedAt shouldBeAbout "2025-08-20T10:00:00Z".toInstant() // 20.8.2025 10:00
                payoutAmount shouldBeEqualComparingTo BigDecimal("500.50")
                firstName shouldBe "Jane"
                gameLevel shouldBe null
                profilePicture shouldBe null
                networkingVisibility shouldBe NetworkingVisibility.DISABLED
            }

            latestStudentPayouts[2].run {
                studentId shouldBe student1.id
                approvedAt shouldBeAbout "2025-06-15T10:00:00Z".toInstant() // 15.6.2025 10:00
                payoutAmount shouldBeEqualComparingTo BigDecimal("1000.00")
                firstName shouldBe "John"
                gameLevel shouldBe null
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "profile-url"
                    imageCompressedUrl shouldBe "profile-url-comp"
                    imageBlurHash shouldBe "123"
                }
                networkingVisibility shouldBe NetworkingVisibility.ENABLED
            }
        }
    }

    @Test
    fun `should return zero values when no approved payouts exist`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        // Create only waiting/denied payouts
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("100.00"),
            payoutDate = LocalDate.of(2024, 5, 10),
        ) // state remains WAITING

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("200.00"),
            payoutDate = LocalDate.of(2024, 6, 15),
            entityModifier = { it.denyAwaiting("Rejected") },
        )

        // Create payout settings for the year
        dataHelper.getGamePayoutSettings(
            year = 2024,
            payoutGoal = BigDecimal("3000.00"),
            payoutOffset = BigDecimal("100.00"),
        )

        val result = underTest.handle(
            GamePayoutOverviewQuery(
                userId = 1.toUUID(),
                limit = 5,
                year = Year.of(2024),
            ),
        )

        result.run {
            currentTotalPayout shouldBeEqualComparingTo BigDecimal("100.00") // 0 + 100.00
            currentPayoutGoal shouldBeEqualComparingTo BigDecimal("3000.00")
            year shouldBe 2024
            latestStudentPayouts.size shouldBe 0
        }
    }
}
