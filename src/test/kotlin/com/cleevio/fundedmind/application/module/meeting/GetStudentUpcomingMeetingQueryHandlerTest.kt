package com.cleevio.fundedmind.application.module.meeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.meeting.query.GetStudentUpcomingMeetingQuery
import com.cleevio.fundedmind.application.module.user.student.exception.StudentNotFoundException
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetStudentUpcomingMeetingQueryHandlerTest(
    @Autowired private val underTest: GetStudentUpcomingMeetingQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get upcoming meeting - verify mappings`() {
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })

        dataHelper.getMeeting(
            id = 1.toUUID(),
            color = Color.YELLOW,
            name = "Meeting 1",
            startAt = "2025-01-01T10:00:00Z".toInstant(), // 1.1.2025
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = true,
            entityModifier = {
                it.changeCoverPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.MEETING_COVER_PHOTO,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-01T00:00:00Z".toInstant(),
            ),
        )

        result.data shouldNotBe null
        result.data!!.run {
            upcomingMeetingId shouldBe 1.toUUID()
            name shouldBe "Meeting 1"
            color shouldBe Color.YELLOW
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            coverPhoto shouldNotBe null
            coverPhoto!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "123"
            }
        }
    }

    @Test
    fun `should return meeting if it is ongoing`() {
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })

        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T12:00:00Z".toInstant(), // 1.1.2025 12:00
            finishAt = "2025-01-01T13:00:00Z".toInstant(), // 1.1.2025 13:00
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = true,
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-01T12:30:00Z".toInstant(), // 1.1.2025 12:30
            ),
        )

        result.data shouldNotBe null
        result.data!!.upcomingMeetingId shouldBe 1.toUUID()
    }

    @Test
    fun `should return null if no upcoming meeting`() {
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE)

        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(), // 1.1.2025
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = true,
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-02T00:00:00Z".toInstant(), // 2.1.2025
            ),
        )

        result.data shouldBe null
    }

    @Test
    fun `should throw if student doesn't exist`() {
        shouldThrow<StudentNotFoundException> {
            underTest.handle(
                GetStudentUpcomingMeetingQuery(
                    studentId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should return meeting if it's only for discord users and student has discord subscription`() {
        // Create a student with active discord subscription
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
            entityModifier = { it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS)) },
        )

        // Create a meeting with empty tiers (no tier-based access) but discord access required
        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = emptyList(), // No tier-based access
            invitedDiscordUsers = true, // Only discord users are invited
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-01T00:00:00Z".toInstant(),
            ),
        )

        // Should return the meeting because student has discord access
        result.data shouldNotBe null
        result.data!!.upcomingMeetingId shouldBe 1.toUUID()
    }

    @Test
    fun `should not return meeting if it's only for discord users and student doesn't have discord subscription`() {
        // Create a student without discord subscription
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
                it.deactivateDiscordSubscription()
            },
        )

        // Create a meeting with empty tiers (no tier-based access) but discord access required
        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(),
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = emptyList(), // No tier-based access
            invitedDiscordUsers = true, // Only discord users are invited
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-01T00:00:00Z".toInstant(),
            ),
        )

        // Should not return any meeting since student doesn't have discord access
        result.data shouldBe null
    }

    @Test
    fun `should return first meeting where student tier was invited`() {
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
        )

        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(), // 1.1.2025
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE), // not invited
        )
        dataHelper.getMeeting(
            id = 2.toUUID(),
            startAt = "2025-01-03T10:00:00Z".toInstant(), // 3.1.2025
            finishAt = "2025-01-03T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
        )
        dataHelper.getMeeting(
            id = 3.toUUID(),
            startAt = "2025-01-02T10:00:00Z".toInstant(), // 2.1.2025
            finishAt = "2025-01-02T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
        )
        dataHelper.getMeeting(
            id = 4.toUUID(),
            startAt = "2025-01-04T10:00:00Z".toInstant(), // 4.1.2025
            finishAt = "2025-01-04T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-01T00:00:00Z".toInstant(),
            ),
        )

        result.data?.upcomingMeetingId shouldBe 3.toUUID() // meeting id=3 is sooner than meeting id=2
    }

    @Test
    fun `if discord access is not required should return meeting based on tier even if student has discord`() {
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        }).also {
            it.discordSubscription shouldBe true
        }

        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(), // 1.1.2025
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.EXCLUSIVE), // not invited
            invitedDiscordUsers = false,
        )

        dataHelper.getMeeting(
            id = 2.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(), // 1.1.2025
            finishAt = "2025-01-01T12:00:00Z".toInstant(),
            invitedTiers = listOf(StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
        )

        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = 1.toUUID(),
                now = "2025-01-01T00:00:00Z".toInstant(),
            ),
        )

        result.data?.upcomingMeetingId shouldBe 2.toUUID()
    }

    @Test
    fun `should get upcoming mentoring meeting when there are no regular meetings`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE)

        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            entityModifier = {
                it.changeMentoringPhoto(
                    fileId = dataHelper.getImage(
                        id = 1.toUUID(),
                        type = FileType.TRADER_MENTORING_PHOTO,
                        originalFileUrl = "mentoring-photo-url",
                        compressedFileUrl = "mentoring-photo-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // Create a product for the trader
        dataHelper.getProduct(traderId = trader.id).also { product ->
            // Create a mentoring for the student and product
            val mentoring = dataHelper.getMentoring(
                studentId = student.id,
                productId = product.id,
            )

            // Create a mentoring meeting
            dataHelper.getMentoringMeeting(
                id = 1.toUUID(),
                mentoringId = mentoring.id,
                color = Color.PURPLE,
                startAt = "2025-01-01T10:00:00Z".toInstant(),
                finishAt = "2025-01-01T12:00:00Z".toInstant(),
                meetingUrl = "mentoring-meeting-url",
            )
        }

        // when
        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = student.id,
                now = "2025-01-01T00:00:00Z".toInstant(), // 1.1.2025 00:00
            ),
        )

        // then
        result.data shouldNotBe null
        result.data!!.run {
            upcomingMeetingId shouldBe 1.toUUID()
            name shouldBe "1:1 Mentoring"
            color shouldBe Color.PURPLE
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            isMentoring shouldBe true
        }
    }

    @Test
    fun `should get closest meeting when both regular and mentoring meetings exist`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE)

        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            entityModifier = {
                it.changeMentoringPhoto(
                    fileId = dataHelper.getImage(
                        id = 1.toUUID(),
                        type = FileType.TRADER_MENTORING_PHOTO,
                        originalFileUrl = "mentoring-photo-url",
                        compressedFileUrl = "mentoring-photo-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // Create a regular meeting that starts later
        dataHelper.getMeeting(
            id = 1.toUUID(),
            startAt = "2025-01-01T10:00:00Z".toInstant(), // 1.1.2025 10:00
            finishAt = "2025-01-01T12:00:00Z".toInstant(), // 1.1.2025 12:00
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
        )

        // Create a product for the trader
        dataHelper.getProduct(traderId = trader.id).also { product ->
            // Create a mentoring for the student and product
            val mentoring = dataHelper.getMentoring(
                studentId = student.id,
                productId = product.id,
            )

            // Create a mentoring meeting that starts earlier
            dataHelper.getMentoringMeeting(
                id = 2.toUUID(),
                mentoringId = mentoring.id,
                color = Color.RED,
                startAt = "2025-01-01T09:00:00Z".toInstant(), // 1.1.2025 09:00 (earlier)
                finishAt = "2025-01-01T12:00:00Z".toInstant(), // 1.1.2025 12:00
                meetingUrl = "mentoring-meeting-url",
            )
        }

        // when
        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = student.id,
                now = "2025-01-01T00:00:00Z".toInstant(), // 1.1.2025 00:00
            ),
        )

        // then pt.1
        result.data shouldNotBe null
        result.data!!.run {
            upcomingMeetingId shouldBe 2.toUUID()
            startAt shouldBe "2025-01-01T09:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            isMentoring shouldBe true
        }

        // Now create a regular meeting that starts even earlier
        dataHelper.getMeeting(
            id = 3.toUUID(),
            startAt = "2025-01-01T08:00:00Z".toInstant(), // 1.1.2025 08:00 (earlier)
            finishAt = "2025-01-01T12:00:00Z".toInstant(), // 1.1.2025 12:00
            invitedTiers = listOf(StudentTier.EXCLUSIVE),
            invitedDiscordUsers = false,
        )

        // when again
        val result2 = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = student.id,
                now = "2025-01-01T00:00:00Z".toInstant(), // 1.1.2025 00:00
            ),
        )

        // then - pt.2
        result2.data shouldNotBe null
        result2.data!!.run {
            upcomingMeetingId shouldBe 3.toUUID()
            startAt shouldBe "2025-01-01T08:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            isMentoring shouldBe false
        }
    }

    @Test
    fun `should get upcoming mentoring meeting - verify mappings`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.EXCLUSIVE).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            entityModifier = {
                it.changeMentoringPhoto(
                    fileId = dataHelper.getImage(
                        id = 1.toUUID(),
                        type = FileType.TRADER_MENTORING_PHOTO,
                        originalFileUrl = "mentoring-photo-url",
                        compressedFileUrl = "mentoring-photo-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // Create a product for the trader
        dataHelper.getProduct(traderId = trader.id).also { product ->
            // Create a mentoring for the student and product
            val mentoring = dataHelper.getMentoring(
                studentId = student.id,
                productId = product.id,
            )

            // Create a mentoring meeting
            dataHelper.getMentoringMeeting(
                id = 1.toUUID(),
                mentoringId = mentoring.id,
                color = Color.PURPLE,
                startAt = "2025-01-01T10:00:00Z".toInstant(),
                finishAt = "2025-01-01T12:00:00Z".toInstant(),
                meetingUrl = "mentoring-meeting-url",
            )
        }

        // when
        val result = underTest.handle(
            GetStudentUpcomingMeetingQuery(
                studentId = student.id,
                now = "2025-01-01T00:00:00Z".toInstant(),
            ),
        )

        // then and all mapped fields
        result.data shouldNotBe null
        result.data!!.run {
            upcomingMeetingId shouldBe 1.toUUID()
            name shouldBe "1:1 Mentoring"
            color shouldBe Color.PURPLE
            startAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            finishAt shouldBe "2025-01-01T12:00:00Z".toInstant()
            isMentoring shouldBe true
            coverPhoto shouldNotBe null
            coverPhoto!!.run {
                imageId shouldBe 1.toUUID()
                imageOriginalUrl shouldBe "mentoring-photo-url"
                imageCompressedUrl shouldBe "mentoring-photo-url-comp"
                imageBlurHash shouldBe "456"
            }
        }
    }
}
