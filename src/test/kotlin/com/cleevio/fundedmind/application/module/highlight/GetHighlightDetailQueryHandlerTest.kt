package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.highlight.query.GetHighlightDetailQuery
import com.cleevio.fundedmind.domain.common.AppButton
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetHighlightDetailQueryHandlerTest(
    @Autowired private val underTest: GetHighlightDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get highlight in calendar - verify mappings`() {
        // given
        dataHelper.getHighlight(
            id = 1.toUUID(),
            title = "Highlight 1",
            description = "Highlight description",
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            linkUrl = "highlight-url",
            button = AppButton("Button", Color.BLUE),
            entityModifier = {
                it.changeImageDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.HIGHLIGHT_DESKTOP_PHOTO,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeImageMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.HIGHLIGHT_MOBILE_PHOTO,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        val result = underTest.handle(
            GetHighlightDetailQuery(
                highlightId = 1.toUUID(),
            ),
        )

        // then
        result.run {
            highlightId shouldBe 1.toUUID()
            published shouldBe false
            imageDesktop shouldNotBe null
            imageDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            imageMobile shouldNotBe null
            imageMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            title shouldBe "Highlight 1"
            description shouldBe "Highlight description"
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe "highlight-url"
            button!!.run {
                text shouldBe "Button"
                color shouldBe Color.BLUE
            }
        }
    }
}
