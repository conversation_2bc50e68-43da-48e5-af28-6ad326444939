package com.cleevio.fundedmind

import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput
import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput.QuestionnaireAnswer
import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput.QuestionnaireQuestion
import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput.QuestionnaireStep
import com.cleevio.fundedmind.domain.user.student.constant.QuestionnaireQuestionType
import java.time.LocalDate

/**
 * Non-persisting data helper
 */
object DataTestHelper {

    fun prepareOnboardingQuestionnaireInput(
        version: LocalDate = LocalDate.of(2025, 1, 31),
        data: List<QuestionnaireInput.QuestionnaireStep> = listOf(
            QuestionnaireStep(
                question = QuestionnaireQuestion(
                    propertyName = "question1",
                    text = "Jak dlouho se věnujete tradingu?",
                    type = QuestionnaireQuestionType.SINGLE_CHOICE,
                ),
                answers = listOf(
                    QuestionnaireInput.QuestionnaireAnswer(
                        propertyName = "BEGINER_PROPERTY",
                        text = "Jsem úplný z<PERSON>čník",
                        selected = true,
                    ),
                    QuestionnaireAnswer(
                        propertyName = "YES_PROPERTY",
                        text = "Jsem pro",
                        selected = false,
                    ),
                ),
            ),
            QuestionnaireStep(
                question = QuestionnaireQuestion(
                    propertyName = "question2",
                    text = "Na jaké trhy se zaměřuješ?",
                    type = QuestionnaireQuestionType.MULTIPLE_CHOICE,
                ),
                answers = listOf(
                    QuestionnaireAnswer(
                        text = "Forex",
                        propertyName = "FOXER",
                        selected = true,
                    ),
                    QuestionnaireAnswer(
                        propertyName = "CRYPTO",
                        text = "Kryptoměny",
                        selected = false,
                    ),
                    QuestionnaireAnswer(
                        propertyName = "STOCK",
                        text = "Akcie",
                        selected = true,
                    ),
                ),
            ),
        ),
    ) = QuestionnaireInput(
        version = version,
        data = data,
    )
}
