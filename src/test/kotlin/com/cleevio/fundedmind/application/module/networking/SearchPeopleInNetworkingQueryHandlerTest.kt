package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fundedmind.application.module.networking.query.SearchPeopleInNetworkingQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class SearchPeopleInNetworkingQueryHandlerTest(
    @Autowired private val underTest: SearchPeopleInNetworkingQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should search for people - verify mappings`() {
        // given
        val requestingUser = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Requesting",
            lastName = "User",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            },
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
            dataHelper.getStudentDiscord(
                studentId = student.id,
                discordId = "requesting_user_discord",
            )
        }

        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            firstName = "John",
            lastName = "Doe",
            biography = "Experienced trader",
            position = "Senior Trader",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            networkingVisibility = NetworkingVisibility.ENABLED,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "trader-url",
                        compressedFileUrl = "trader-url-comp",
                        blurHash = "trader-blur",
                    ).id,
                )
            },
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        val student = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Jane",
            lastName = "Smith",
            biography = "Learning student",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "student-url",
                        compressedFileUrl = "student-url-comp",
                        blurHash = "student-blur",
                    ).id,
                )
            },
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
            dataHelper.getStudentDiscord(
                studentId = student.id,
                discordId = "jane_discord",
            )
        }

        // when
        val slice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content shouldHaveSize 2
        slice.hasMore shouldBe false
        slice.lastId shouldNotBe null

        slice.content.find { it.data.id == trader.id }!!.data.run {
            id shouldBe trader.id
            role shouldBe UserRole.TRADER
            firstName shouldBe "John"
            lastName shouldBe "Doe" // requesting user can see last names (has discord subscription)
            biography shouldBe "Experienced trader"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "trader-url"
                imageCompressedUrl shouldBe "trader-url-comp"
                imageBlurHash shouldBe "trader-blur"
            }
            studentDiscordId shouldBe null
            gameLevel shouldBe null
            traderBadgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            traderPosition shouldBe "Senior Trader"
        }

        slice.content.find { it.data.id == student.id }!!.data.run {
            id shouldBe student.id
            role shouldBe UserRole.STUDENT
            firstName shouldBe "Jane"
            lastName shouldBe "Smith" // requesting user can see last names (has discord subscription)
            biography shouldBe "Learning student"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "student-url"
                imageCompressedUrl shouldBe "student-url-comp"
                imageBlurHash shouldBe "student-blur"
            }
            studentDiscordId shouldBe "jane_discord"
            gameLevel shouldBe GameLevel.FIVE
            traderBadgeColor shouldBe null
            traderPosition shouldBe null
        }
    }

    @Test
    fun `should not return people with networking disabled`() {
        // given
        val requestingUser = dataHelper.getStudent(
            id = 1.toUUID(),
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // People with networking enabled
        dataHelper.getTrader(
            id = 2.toUUID(),
            firstName = "Enabled",
            lastName = "Trader",
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Enabled",
            lastName = "Student",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // People with networking disabled
        dataHelper.getTrader(
            id = 4.toUUID(),
            firstName = "Disabled",
            lastName = "Trader",
            networkingVisibility = NetworkingVisibility.DISABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        dataHelper.getStudent(
            id = 5.toUUID(),
            firstName = "Disabled",
            lastName = "Student",
            networkingVisibility = NetworkingVisibility.DISABLED,
            studentTier = StudentTier.EXCLUSIVE,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // when
        val slice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content shouldHaveSize 2
        slice.content.all { it.data.firstName == "Enabled" } shouldBe true
    }

    @Test
    fun `should hide last names for students without discord subscription`() {
        // given
        val requestingUser = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Requesting",
            lastName = "User",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
            // No discord subscription
        }

        dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Jane",
            lastName = "Smith",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // when
        val slice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content shouldHaveSize 1
        slice.content.single().data.run {
            firstName shouldBe "Jane"
            lastName shouldBe null
        }
    }

    @Test
    fun `should show last names for traders`() {
        // given
        val requestingUser = dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "Requesting",
            lastName = "Trader",
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Jane",
            lastName = "Smith",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // when
        val slice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content shouldHaveSize 1
        slice.content.single().data.run {
            firstName shouldBe "Jane"
            lastName shouldBe "Smith" // Traders can always see last names
        }
    }

    @Test
    fun `should return empty slice when no people found`() {
        // given
        val requestingUser = dataHelper.getStudent(
            id = 1.toUUID(),
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // when
        val slice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // then
        slice.content shouldHaveSize 0
        slice.hasMore shouldBe false
        slice.lastId shouldBe null
    }

    @Test
    fun `should search by full name (first name + last name)`() {
        // given
        val requestingUser = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Requesting",
            lastName = "User",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            },
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Create users with different names
        val joeSmith = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Joe",
            lastName = "Smith",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val joeDoe = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Joe",
            lastName = "Doe",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val janeDoe = dataHelper.getTrader(
            id = 4.toUUID(),
            firstName = "Jane",
            lastName = "Doe",
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        // Test searching by full name "Joe Doe"
        val fullNameSlice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = "Joe Doe"),
            ),
        )

        // Then - should match only Joe Doe
        fullNameSlice.content shouldHaveSize 1
        fullNameSlice.content.single().data.run {
            id shouldBe joeDoe.id
            firstName shouldBe "Joe"
            lastName shouldBe "Doe"
        }

        // Test searching by partial first name
        val firstNameSlice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = "Joe"),
            ),
        )

        // Then - should match both Joe Smith and Joe Doe
        firstNameSlice.content shouldHaveSize 2
        firstNameSlice.content.any { it.data.id == joeSmith.id } shouldBe true
        firstNameSlice.content.any { it.data.id == joeDoe.id } shouldBe true

        // Test searching by partial last name
        val lastNameSlice = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = requestingUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = "Doe"),
            ),
        )

        // Then - should match both Joe Doe and Jane Doe
        lastNameSlice.content shouldHaveSize 2
        lastNameSlice.content.any { it.data.id == joeDoe.id } shouldBe true
        lastNameSlice.content.any { it.data.id == janeDoe.id } shouldBe true
    }

    @Test
    fun `student user should only see game level for students with enabled level visibility`() {
        // Create students with different LevelVisibility settings
        val studentWithEnabledLevel = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Enabled",
            lastName = "Level",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val studentWithDisabledLevel = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Disabled",
            lastName = "Level",
            gameLevel = GameLevel.NINE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.DISABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Student requesting user (cannot always see game level)
        val studentUser = dataHelper.getStudent(id = 3.toUUID()).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val studentResult = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = studentUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // Student should see game level only for students with LevelVisibility.ENABLED
        studentResult.content shouldHaveSize 2
        studentResult.content.find { it.data.id == studentWithEnabledLevel.id }!!.data.run {
            firstName shouldBe "Enabled"
            gameLevel shouldBe GameLevel.FIVE // Level is visible because LevelVisibility is ENABLED
        }
        studentResult.content.find { it.data.id == studentWithDisabledLevel.id }!!.data.run {
            firstName shouldBe "Disabled"
            gameLevel shouldBe null // Level is hidden because LevelVisibility is DISABLED
        }
    }

    @Test
    fun `trader user should always see game level regardless of level visibility`() {
        // Create students with different LevelVisibility settings
        val studentWithEnabledLevel = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Enabled",
            lastName = "Level",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val studentWithDisabledLevel = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Disabled",
            lastName = "Level",
            gameLevel = GameLevel.NINE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.DISABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Trader requesting user (can always see game level)
        val traderUser = dataHelper.getTrader(id = 4.toUUID()).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        val traderResult = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = traderUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // Trader should see game level for all students, regardless of LevelVisibility
        traderResult.content shouldHaveSize 2
        traderResult.content.find { it.data.id == studentWithEnabledLevel.id }!!.data.run {
            firstName shouldBe "Enabled"
            gameLevel shouldBe GameLevel.FIVE // Level is visible because trader can always see game level
        }
        traderResult.content.find { it.data.id == studentWithDisabledLevel.id }!!.data.run {
            firstName shouldBe "Disabled"
            gameLevel shouldBe GameLevel.NINE // Level is visible because trader can always see game level
        }
    }

    @Test
    fun `admin user should always see game level regardless of level visibility`() {
        // Create students with different LevelVisibility settings
        val studentWithEnabledLevel = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Enabled",
            lastName = "Level",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val studentWithDisabledLevel = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Disabled",
            lastName = "Level",
            gameLevel = GameLevel.NINE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.DISABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Admin requesting user (can always see game level)
        val adminUser = dataHelper.getAppUser(id = 5.toUUID(), userRole = UserRole.ADMIN)

        val adminResult = underTest.handle(
            SearchPeopleInNetworkingQuery(
                userId = adminUser.id,
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchPeopleInNetworkingQuery.Filter(searchString = null),
            ),
        )

        // Admin should see game level for all students, regardless of LevelVisibility
        adminResult.content shouldHaveSize 2
        adminResult.content.find { it.data.id == studentWithEnabledLevel.id }!!.data.run {
            firstName shouldBe "Enabled"
            gameLevel shouldBe GameLevel.FIVE // Level is visible because admin can always see game level
        }
        adminResult.content.find { it.data.id == studentWithDisabledLevel.id }!!.data.run {
            firstName shouldBe "Disabled"
            gameLevel shouldBe GameLevel.NINE // Level is visible because admin can always see game level
        }
    }
}
