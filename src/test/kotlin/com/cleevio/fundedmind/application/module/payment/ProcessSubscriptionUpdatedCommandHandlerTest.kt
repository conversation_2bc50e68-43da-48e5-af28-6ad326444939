package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionDataPort
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionUpdatedCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.SubscriptionStatus
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import com.cleevio.fundedmind.truncatedShouldBe
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ProcessSubscriptionUpdatedCommandHandlerTest(
    @Autowired private val underTest: ProcessSubscriptionUpdatedCommandHandler,
    @Autowired private val studentRepository: StudentRepository,
) : IntegrationTest() {

    @Test
    fun `process subscription update - should update discord subscription expiration date`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            hubspotIdentifier = 1,
        ).also {
            dataHelper.getStudent(
                id = 1.toUUID(),
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = {
                    it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                },
            )
        }

        every {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
        } returns GetSubscriptionDataPort.SubscriptionData(
            subscriptionId = "sub_1",
            startsAt = "2025-05-01T10:00:00Z".toInstant(),
            endsAt = "2025-06-01T10:00:00Z".toInstant(),
            subscriptionCancelAt = null,
            status = SubscriptionStatus.ACTIVE,
        )

        every {
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-06-01T10:00:00Z".toInstant(),
            )
        } just Runs

        // when
        underTest.handle(
            ProcessSubscriptionUpdatedCommand(
                subscriptionIdentifier = "sub_1",
                customerIdentifier = "cus_1",
                productIdentifier = "prod_DISCORD",
            ),
        )

        // then
        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            discordSubscription shouldBe true
            discordSubscriptionExpiresAt!! truncatedShouldBe "2025-06-01T10:00:00Z".toInstant()
        }

        verify {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-06-01T10:00:00Z".toInstant(),
            )
        }

        verify(exactly = 0) {
            sendEmailService.sendEmailDiscordCancelled(any(), any())
        }
    }

    @Test
    fun `process subscription update - should send cancellation email when subscription is cancelled`() {
        // given
        dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            stripeIdentifier = "cus_1",
            hubspotIdentifier = 1,
        ).also {
            dataHelper.getStudent(
                id = 1.toUUID(),
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = {
                    it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                },
            )
        }

        every {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
        } returns GetSubscriptionDataPort.SubscriptionData(
            subscriptionId = "sub_1",
            startsAt = "2025-05-01T10:00:00Z".toInstant(),
            endsAt = "2025-06-01T10:00:00Z".toInstant(),
            subscriptionCancelAt = "2025-06-01T10:00:00Z".toInstant(),
            status = SubscriptionStatus.ACTIVE,
        )

        every {
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-06-01T10:00:00Z".toInstant(),
            )
        } just Runs

        // when
        underTest.handle(
            ProcessSubscriptionUpdatedCommand(
                subscriptionIdentifier = "sub_1",
                customerIdentifier = "cus_1",
                productIdentifier = "prod_DISCORD",
            ),
        )

        // then
        studentRepository.findByIdOrNull(1.toUUID())!!.run {
            discordSubscription shouldBe true
            discordSubscriptionExpiresAt!! truncatedShouldBe "2025-06-01T10:00:00Z".toInstant()
        }

        verify {
            stripeService.getSubscriptionBySubscriptionId("sub_1")
            hubspotService.updateCrmUserPremiumDiscordAccess(
                hubspotIdentifier = 1,
                premiumDiscordAccessEnds = "2025-06-01T10:00:00Z".toInstant(),
            )
        }
    }
}
