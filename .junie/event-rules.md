# Event Rules

## Overview

This document outlines the rules and patterns for implementing events in our projects following the Event-Driven Architecture (EDA) principles. It serves as a guide for developers to understand how to work with events for communicating between different modules of the system.

## Event-Driven Architecture

Event-Driven Architecture (EDA) is a design pattern where components communicate with each other through events:

- **Events**: Represent something that has happened in the system (past tense)
- **Event Publishers**: Components that publish events when something happens
- **Event Listeners**: Components that listen for and react to events

This approach allows for loose coupling between components, as publishers don't need to know who is listening to their events, and listeners don't need to know who published the events they're listening to.

### Key Principles

1. **Loose Coupling**: Components are decoupled from each other, communicating only through events
2. **Single Responsibility**: Each event represents a single thing that happened
3. **Immutability**: Events are immutable data structures
4. **Minimal Data**: Events should contain only the minimal data needed (typically just IDs)

## Naming Conventions

### Events

- Events should be named in the past tense, describing something that has happened
- Use the format `[Entity][Action]Event` (e.g., `UserCreatedEvent`, `OrderCompletedEvent`)
- Events should be defined as data classes

```kotlin
data class UserCreatedEvent(
    val userId: UUID,
)
```

### Event Listeners

- Event listeners should be named based on the module they belong to and the events they listen to
- If the listener is in the same module as the event publisher, use `[Entity]EventListener` (e.g., `UserEventListener`)
- If the listener is in a different module than the event publisher, use `[PublisherModule][ListenerModule]EventListener` (e.g., `OrderUserEventListener` for events published by the Order module and listened to by the User module)

```kotlin
@Component
class UserEventListener(
    // Dependencies
) {
    @EventListener
    fun handleUserCreatedEvent(event: UserCreatedEvent) {
        // Handle the event
    }
}

```

## Package Structure

Events and event listeners should be organized in a consistent package structure:

```
com.example.project
├── application
│   └── module
│       └── user
│           ├── service              # Services to process events
│           │   └── UserCreatedProcessingService.kt
│           ├── event                # Event definitions
│           │   └── UserCreatedEvent.kt
│           └── event.listener       # Event listeners
│               └── UserEventListener.kt
```

## Implementation Patterns

### Event Definition

Events should be defined as immutable data classes that contain only the minimal data needed, typically just IDs:

```kotlin
package com.example.project.application.module.user.event

import java.util.UUID

data class UserCreatedEvent(
    val userId: UUID,
)
```

### Event Listener

Event listeners should be Spring components that use the `@EventListener` or (`@Async` + `@TransactionEventListener`) annotation to handle events:

```kotlin
package com.example.project.application.module.user.event.listener

import com.example.project.application.module.user.event.UserCreatedEvent
import com.example.project.application.module.user.service.UserService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class UserEventListener(
    private val userService: UserService,
) {
    @EventListener
    fun handleUserCreatedEvent(event: UserCreatedEvent) {
        userService.processUserCreated(event.userId)
    }
}
```

### Event Publishing

Events should be published using Spring's `ApplicationEventPublisher` at application layer:

```kotlin
@Component
class UserService(
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    fun createUser(name: String, email: String): UUID {
        // Create user logic
        val userId = // ...
        
        // Publish event
        applicationEventPublisher.publishEvent(UserCreatedEvent(userId))
        
        return userId
    }
}
```

## @EventListener vs @Async + @TransactionEventListener

Spring provides two main annotations for event handling:

### @EventListener

- Processes events synchronously
- Executes in the same transaction as the event publisher
- Use when the event handling needs to be part of the same transaction as the event publishing

```kotlin
@Component
class UserEventListener(
    private val userService: UserService,
) {
    @EventListener
    fun handleUserCreatedEvent(event: UserCreatedEvent) {
        userService.processUserCreated(event.userId)
    }
}

```

### @TransactionEventListener + @Async

- `@TransactionEventListener` allows specifying when the event should be processed relative to the transaction (BEFORE_COMMIT, AFTER_COMMIT, AFTER_ROLLBACK, AFTER_COMPLETION)
- `@Async` makes the event processing happen in a separate thread
- Use when the event handling should not block the main transaction and can be processed asynchronously

```kotlin
@Component
class UserEventListener(
    private val userService: UserService,
) {
    @Async
    @TransactionEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleUserCreatedEvent(event: UserCreatedEvent) {
        userService.processUserCreated(event.userId)
    }
}

```

## Best Practices

1. **Keep Events Simple**: Events should be simple data classes with only the necessary IDs.
2. **Use Immutable Data Structures**: Events should be immutable.
3. **Name Events in Past Tense**: Events should be named in the past tense to indicate that they represent something that has already happened.
4. **Separate Event Logic**: Event listeners should not contain business logic directly but should call services that process the event.
5. **Handle Failures Gracefully**: Event listeners should handle failures gracefully, possibly by retrying or logging errors.
6. **Document Events**: Include comments or documentation that explains the purpose of the event and when it is published.
7. **Consider Transaction Boundaries**: Use `@TransactionEventListener` with appropriate phase to control when events are processed relative to transactions.
8. **Use Async Processing When Appropriate**: Use `@Async` for event listeners that don't need to block the main thread.
9. **Organize Events by Module**: Keep events and their listeners organized by module to maintain a clean package structure.
10. **Minimize Event Data**: Events should contain only the minimal data needed, typically just IDs.

## Complete Examples

### Simple Event and Listener

#### Event Definition

```kotlin
package com.example.project.application.module.user.event

import java.util.UUID

data class UserCreatedEvent(
    val userId: UUID,
)
```

#### Event Listener in Same Module

```kotlin
package com.example.project.application.module.user.event.listener

import com.example.project.application.module.user.event.UserCreatedEvent
import com.example.project.application.module.user.service.UserService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class UserEventListener(
    private val userCreatedProcessingService: UserCreatedProcessingService,
) {
    @EventListener
    fun handleUserCreatedEvent(event: UserCreatedEvent) {
		userCreatedProcessingService.process(event.userId)
    }
}
```

#### Event Listener in Different Module

```kotlin
package com.example.project.application.module.notification.event.listener

import com.example.project.application.module.notification.service.NotificationService
import com.example.project.application.module.user.event.UserCreatedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener

@Component
class UserNotificationEventListener(
	private val newUserNotificationService: NewUserNotificationService,
) {
	@Async
	@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
	fun handleUserCreatedEvent(event: UserCreatedEvent) {
		newUserNotificationService.process(event.userId)
	}
}
```

### Asynchronous Event Processing

#### Event Definition

```kotlin
package com.example.project.application.module.order.event

import java.util.UUID

data class OrderCompletedEvent(
    val orderId: UUID,
)
```

#### Asynchronous Event Listener

```kotlin
package com.example.project.application.module.invoice.event.listener

import com.example.project.application.module.invoice.service.InvoiceService
import com.example.project.application.module.order.event.OrderCompletedEvent
import org.springframework.context.event.TransactionPhase
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionEventListener

@Component
class OrderInvoiceEventListener(
    private val newOrderInvoiceService: NewOrderInvoiceService,
) {
    @Async
    @TransactionEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleOrderCompletedEvent(event: OrderCompletedEvent) {
		newOrderInvoiceService.process(event.orderId)
    }
}
```

### Multiple Events in One File

For related events, it's common to group them in a single file:

```kotlin
package com.example.project.application.module.course.event

import java.util.UUID

data class CourseCreatedEvent(
    val courseId: UUID,
)

data class CourseUpdatedEvent(
    val courseId: UUID,
)

data class CourseDeletedEvent(
    val courseId: UUID,
)

data class CoursePublishedEvent(
    val courseId: UUID,
)

data class CourseHiddenEvent(
    val courseId: UUID,
)
```

### Processing service
Services are intended to contain the application's business logic. This logic is then in one place and can be called from multiple places such as events, cron jobs, command handlers, etc.

```kotlin
import org.springframework.transaction.annotation.Transactional

@Service
class NewUserNotificationService(
	private val userFinderService: UserFinderService,
) {

	private val logger = logger()

	@Transactional
	fun process(userId: UUID) {
		val user = userFinderService.getById(userId)

		// processing logic gere ...
	}
}

```

## Conclusion

Following these event rules and patterns ensures that event-driven communication in the project is maintainable, testable, and consistent. It also facilitates onboarding of new developers by providing clear guidelines for working with events in an Event-Driven Architecture.
