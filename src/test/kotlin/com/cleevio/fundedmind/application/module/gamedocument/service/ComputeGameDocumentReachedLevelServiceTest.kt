package com.cleevio.fundedmind.application.module.gamedocument.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class ComputeGameDocumentReachedLevelServiceTest(
    @Autowired private val underTest: ComputeGameDocumentReachedLevelService,
) : IntegrationTest() {

    @Test
    fun `should upgrade from level ONE to TWO for BACKTESTING document if STRATEGY was seen`() {
        // Create a student with level ONE
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.ONE,
            )
        }

        // Create a strategy course and mark it as completed
        dataHelper.getCourse(
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
        ).also { course ->
            dataHelper.getCourseProgress(
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-09-01T10:00:00Z".toInstant(),
            )
        }

        val result = underTest.computeReachedLevel(
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
        )

        result shouldBe GameLevel.TWO
    }

    @Test
    fun `should NOT upgrade from level ONE to TWO for BACKTESTING document without seen STRATEGY`() {
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.ONE,
            )
        }

        val result = underTest.computeReachedLevel(
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
        )

        result shouldBe GameLevel.ONE // no strategy course was seen therefore level remains ONE
    }

    @ParameterizedTest
    @EnumSource(
        value = GameLevel::class,
        names = ["TWO", "THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "NINE", "TEN"],
    )
    fun `should keep current level for BACKTESTING if student is not at level ONE`(currentLevel: GameLevel) {
        // Create a student with level higher than ONE
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = currentLevel,
            )
        }

        val result = underTest.computeReachedLevel(
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
        )

        result shouldBe currentLevel
    }

    @ParameterizedTest
    @EnumSource(value = GameLevel::class, names = ["ONE", "TWO"])
    fun `should upgrade to level THREE for CERTIFICATE document`(currentLevel: GameLevel) {
        // Create a student with level ONE or TWO
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = currentLevel,
            )
        }

        val result = underTest.computeReachedLevel(
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
        )

        result shouldBe GameLevel.THREE
    }

    @ParameterizedTest
    @EnumSource(
        value = GameLevel::class,
        names = ["THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "NINE", "TEN"],
    )
    fun `should keep current level for CERTIFICATE if student is not below level THREE`(currentLevel: GameLevel) {
        // Create a student with level THREE or higher
        val user = dataHelper.getAppUser(id = 115.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = currentLevel,
            )
        }

        val result = underTest.computeReachedLevel(
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
        )

        result shouldBe currentLevel
    }

    @Test
    fun `should throw exception when payout amount is null for payout document`() {
        // Create a student
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to compute with null payout amount
        shouldThrow<GameDocumentPayoutAmountIsRequiredException> {
            underTest.computeReachedLevel(
                studentId = user.id,
                type = GameDocumentType.PAYOUT,
                payoutAmount = null,
            )
        }
    }

    @Test
    fun `should throw exception when payout amount is negative`() {
        // Create a student
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to compute with negative payout amount
        shouldThrow<GameDocumentPayoutAmountCannotBeNegativeException> {
            underTest.computeReachedLevel(
                studentId = user.id,
                type = GameDocumentType.PAYOUT,
                payoutAmount = BigDecimal("-100.00"),
            )
        }
    }

    @Test
    fun `should determine level based on total payout amount for PAYOUT document`() {
        // Create a student with level THREE
        val student = dataHelper.getAppUser(id = 116.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        // Create an approved payout document for the student
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 4_000.toBigDecimal(),
            entityModifier = { it.approveAwaiting() },
        )

        // Compute level with a new payout amount
        val result = underTest.computeReachedLevel(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 13_000.toBigDecimal(),
        )

        // Total payout is 4000 + 13000 = 17000, which should be level SIX
        result shouldBe GameLevel.SIX
    }

    @Test
    fun `should determine level SEVEN based on total payout amount for PAYOUT document`() {
        // Create a student with level ONE (even before backtesting and certificate)
        val student = dataHelper.getAppUser(id = 117.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.ONE,
            )
        }

        // Compute level with a large payout amount
        val result = underTest.computeReachedLevel(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 36_000.toBigDecimal(),
        )

        // Total payout is 36000, which should be level SEVEN
        result shouldBe GameLevel.SEVEN
    }
}
