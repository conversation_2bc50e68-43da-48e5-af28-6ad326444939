package com.cleevio.fundedmind.application.module.gamepayout

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.gamepayout.port.GameLeaderboardPort
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.util.UUID

class GameLeaderboardMapToPlayersWithPositionUnitTest {

    @Test
    fun `mapToPlayersWithPosition - should assign positions correctly with no ties`() {
        val students = listOf(
            student(payoutAmount = 1000.toBigDecimal(), firstName = "Alice"),
            student(payoutAmount = 800.toBigDecimal(), firstName = "Bob"),
            student(payoutAmount = 600.toBigDecimal(), firstName = "Charlie"),
        )

        val result = students.mapToPlayersWithPosition()

        result.size shouldBe 3
        result[0].run {
            position shouldBe 1
            student.firstName shouldBe "Alice"
        }
        result[1].run {
            position shouldBe 2
            student.firstName shouldBe "Bob"
        }
        result[2].run {
            position shouldBe 3
            student.firstName shouldBe "Charlie"
        }
    }

    @Test
    fun `mapToPlayersWithPosition - should handle shared positions with same payout amounts`() {
        val students = listOf(
            student(payoutAmount = 1000.toBigDecimal(), firstName = "Alice"),
            student(payoutAmount = 800.toBigDecimal(), firstName = "Bob"),
            student(payoutAmount = 800.toBigDecimal(), firstName = "Charlie"),
            student(payoutAmount = 600.toBigDecimal(), firstName = "David"),
        )

        val result = students.mapToPlayersWithPosition()

        result.size shouldBe 4
        result[0].run {
            position shouldBe 1 // Alice with 1000
            student.firstName shouldBe "Alice"
        }
        result[1].run {
            position shouldBe 2 // Bob with 800
            student.firstName shouldBe "Bob"
        }
        result[2].run {
            position shouldBe 2 // Charlie with 800 (shared position)
            student.firstName shouldBe "Charlie"
        }
        result[3].run {
            position shouldBe 4 // David with 600 (position 3 is skipped)
            student.firstName shouldBe "David"
        }
    }

    @Test
    fun `mapToPlayersWithPosition - should handle multiple shared positions`() {
        val students = listOf(
            student(payoutAmount = 1000.toBigDecimal(), firstName = "Alice"),
            student(payoutAmount = 800.toBigDecimal(), firstName = "Bob"),
            student(payoutAmount = 800.toBigDecimal(), firstName = "Charlie"),
            student(payoutAmount = 800.toBigDecimal(), firstName = "David"),
            student(payoutAmount = 600.toBigDecimal(), firstName = "Eve"),
            student(payoutAmount = 600.toBigDecimal(), firstName = "Frank"),
        )

        val result = students.mapToPlayersWithPosition()

        result.size shouldBe 6
        result[0].run {
            position shouldBe 1 // Alice with 1000
            student.firstName shouldBe "Alice"
        }
        result[1].run {
            position shouldBe 2 // Bob with 800
            student.firstName shouldBe "Bob"
        }
        result[2].run {
            position shouldBe 2 // Charlie with 800
            student.firstName shouldBe "Charlie"
        }
        result[3].run {
            position shouldBe 2 // David with 800
            student.firstName shouldBe "David"
        }
        result[4].run {
            position shouldBe 5 // Eve with 600 (positions 3,4 skipped)
            student.firstName shouldBe "Eve"
        }
        result[5].run {
            position shouldBe 5 // Frank with 600
            student.firstName shouldBe "Frank"
        }
    }

    @Test
    fun `mapToPlayersWithPosition - should handle all students with same payout amount`() {
        val students = listOf(
            student(payoutAmount = 500.toBigDecimal(), firstName = "Alice"),
            student(payoutAmount = 500.toBigDecimal(), firstName = "Bob"),
            student(payoutAmount = 500.toBigDecimal(), firstName = "Charlie"),
        )

        val result = students.mapToPlayersWithPosition()

        result.size shouldBe 3
        result[0].run {
            position shouldBe 1 // Alice
            student.firstName shouldBe "Alice"
        }
        result[1].run {
            position shouldBe 1 // Bob (shared position)
            student.firstName shouldBe "Bob"
        }
        result[2].run {
            position shouldBe 1 // Charlie (shared position)
            student.firstName shouldBe "Charlie"
        }
    }

    @Test
    fun `mapToPlayersWithPosition - should handle single student`() {
        val students = listOf(
            student(payoutAmount = 1000.toBigDecimal(), firstName = "Alice"),
        )

        val result = students.mapToPlayersWithPosition()

        result.size shouldBe 1
        result[0].run {
            position shouldBe 1
            student.firstName shouldBe "Alice"
        }
    }

    @Test
    fun `mapToPlayersWithPosition - should handle empty list`() {
        val students = emptyList<GameLeaderboardPort.StudentWithPayout>()

        val result = students.mapToPlayersWithPosition()

        result.size shouldBe 0
    }

    private fun student(
        studentId: UUID = UUID.randomUUID(),
        firstName: String = "John",
        lastName: String = "Doe",
        city: String? = "Test City",
        gameLevel: GameLevel = GameLevel.ONE,
        profilePicture: ImageResult? = null,
        payoutAmount: BigDecimal,
    ) = GameLeaderboardPort.StudentWithPayout(
        studentId = studentId,
        firstName = firstName,
        lastName = lastName,
        city = city,
        gameLevel = gameLevel,
        profilePicture = profilePicture,
        payoutAmount = payoutAmount,
        networkingVisibility = NetworkingVisibility.ENABLED,
    )
}
