package com.cleevio.fundedmind.application.module.comment.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.comment.command.UserUnlikesCommentCommand
import com.cleevio.fundedmind.domain.comment.CommentLikeRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class UserUnlikesCommentCommandHandlerTest(
    @Autowired private val underTest: UserUnlikesCommentCommandHandler,
    @Autowired private val commentLikeRepository: CommentLikeRepository,
) : IntegrationTest() {

    @Test
    fun `should unlike existing comment for existing lesson`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID())
        val trader = dataHelper.getTrader(id = 0.toUUID())
        dataHelper.getAppUser(id = 1.toUUID())
        val trader2 = dataHelper.getTrader(id = 1.toUUID())

        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = trader.id)
        val courseModule = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 555.toUUID(), courseModuleId = courseModule.id)
        dataHelper.getLessonComment(
            id = 1000.toUUID(),
            appUserId = trader.id,
            lessonId = lesson.id,
        )
        dataHelper.getCommentLike(id = 1001.toUUID(), appUserId = trader.id, commentId = 1000.toUUID())
        dataHelper.getCommentLike(id = 1002.toUUID(), appUserId = trader2.id, commentId = 1000.toUUID())

        // when
        underTest.handle(
            UserUnlikesCommentCommand(
                appUserId = trader.id,
                commentId = 1000.toUUID(),
            ),
        )

        // then
        commentLikeRepository.findAll().run {
            this.size shouldBe 1
            this.first().id shouldBe 1002.toUUID()
        }
    }
}
