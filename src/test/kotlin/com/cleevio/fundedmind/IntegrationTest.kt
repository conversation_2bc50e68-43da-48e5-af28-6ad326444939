package com.cleevio.fundedmind

import com.cleevio.fundedmind.adapter.out.calendly.CalendlyService
import com.cleevio.fundedmind.adapter.out.discord.DiscordService
import com.cleevio.fundedmind.adapter.out.fakturoid.FakturoidService
import com.cleevio.fundedmind.adapter.out.firebase.FirebaseService
import com.cleevio.fundedmind.adapter.out.hubspot.connector.HubspotService
import com.cleevio.fundedmind.adapter.out.stripe.StripeConnector
import com.cleevio.fundedmind.adapter.out.stripe.StripeService
import com.cleevio.fundedmind.adapter.out.zapier.ZapierService
import com.cleevio.fundedmind.application.common.port.out.CheckoutSessionPort
import com.cleevio.fundedmind.application.common.port.out.ExistsExternalProductPort
import com.cleevio.fundedmind.application.common.port.out.PaymentCustomerPort
import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.google.cloud.firestore.Firestore
import com.google.cloud.storage.Storage
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.FirebaseAuth
import com.ninjasquad.springmockk.MockkBean
import io.mockk.confirmVerified
import io.mockk.unmockkStatic
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.util.AopTestUtils
import java.time.Instant
import java.time.LocalDate
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

@SpringBootTest
@ActiveProfiles("test")
@MockkBean(
    classes = [
        FirebaseAuth::class,
        FirebaseApp::class,
        Firestore::class,
        Storage::class,
    ],
    relaxed = true,
)
@ContextConfiguration(
    classes = [
        ContainerTestConfiguration::class,
        IntegrationTestAsyncConfig::class,
    ],
)
class IntegrationTest {
    @Autowired
    private lateinit var countingAsyncUncaughtExceptionHandler: CountingAsyncUncaughtExceptionHandler

    @Autowired
    private lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    internal lateinit var dataHelper: IntegrationDataTestHelper

    @MockkBean
    internal lateinit var existsExternalProductPort: ExistsExternalProductPort

    @MockkBean
    internal lateinit var stripeService: StripeService

    @MockkBean
    internal lateinit var paymentCustomerPort: PaymentCustomerPort

    @MockkBean
    internal lateinit var checkoutSessionPort: CheckoutSessionPort

    @MockkBean
    internal lateinit var sendEmailService: SendEmailService

    @MockkBean
    internal lateinit var hubspotService: HubspotService

    @MockkBean
    internal lateinit var calendlyService: CalendlyService

    @MockkBean
    internal lateinit var firebaseService: FirebaseService

    @MockkBean
    internal lateinit var discordService: DiscordService

    @MockkBean
    internal lateinit var fakturoidService: FakturoidService

    @MockkBean
    internal lateinit var zapierService: ZapierService

    @MockkBean
    internal lateinit var stripeConnector: StripeConnector

    // ! Add mockk beans to the list of verified mocks
    @AfterEach
    fun afterEach() {
        confirmVerified(
            existsExternalProductPort,
            stripeService,
            paymentCustomerPort,
            checkoutSessionPort,
            sendEmailService,
            hubspotService,
            calendlyService,
            firebaseService,
            discordService,
            fakturoidService,
            zapierService,
            stripeConnector,
        )
        unmockkStatic(Instant::class)
        unmockkStatic(LocalDate::class)
        countingAsyncUncaughtExceptionHandler.assertNoExceptionsThrownInAsyncThreads()
    }

    @BeforeEach
    fun beforeEach() {
        jdbcTemplate.execute(TRUNCATE_ALL_TABLES_SQL)
        countingAsyncUncaughtExceptionHandler.reset()
    }
}

inline fun <reified T : Any, R> T.getPrivatePropertyOfProxy(name: String): R? =
    AopTestUtils.getTargetObject<T>(this).getPrivateProperty(name)

@Suppress("UNCHECKED_CAST")
inline fun <reified T : Any, R> T.getPrivateProperty(name: String): R? = T::class
    .memberProperties
    .firstOrNull { it.name == name }
    ?.apply { isAccessible = true }
    ?.get(this) as? R

private const val TRUNCATE_ALL_TABLES_SQL = """
DO
${'$'}do${'$'}
	BEGIN
		EXECUTE
			(SELECT 'TRUNCATE TABLE ' || string_agg(oid::regclass::text, ', ') || ' CASCADE'
			 FROM   pg_class
			 WHERE  relkind = 'r'  -- only tables
			   AND    relnamespace = 'public'::regnamespace
			   AND relname NOT IN ('databasechangelog', 'databasechangeloglock')
			);
	END
${'$'}do${'$'};
"""
