package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.port.out.GetDefaultProductPricePort.ProductPrice
import com.cleevio.fundedmind.application.common.port.out.GetDefaultProductPricePort.ProductPriceMap
import com.cleevio.fundedmind.application.module.product.exception.ProductPriceNotFoundException
import com.cleevio.fundedmind.application.module.product.query.SearchProductQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.MonetaryCurrency
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.parseIntegerList
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID

class SearchProductQueryHandlerTest(
    @Autowired private val underTest: SearchProductQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should search for product - verify mappings`() {
        every { stripeService.getPriceMap(listOf("prod_1")) } returns ProductPriceMap(
            listOf(
                ProductPrice(
                    "prod_1",
                    MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
            ),
        )

        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "John",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
            dataHelper.getProduct(
                id = 1.toUUID(),
                traderId = trader.id,
                name = "product",
                sessionsCount = 5,
                validityInDays = null,
                description = "product-description",
                stripeIdentifier = "prod_1",
            )
        }

        val slice = underTest.handle(
            SearchProductQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchProductQuery.Filter(searchString = null),
            ),
        )

        slice.content shouldHaveSize 1
        slice.hasMore shouldBe false
        slice.lastId shouldBe 1.toUUID()
        slice.content.single().run {
            productId shouldBe 1.toUUID()
            name shouldBe "product"
            price.run {
                unitAmount shouldBe 100
                currency shouldBe MonetaryCurrency.CZK
                amount shouldBeEqualComparingTo 1.toBigDecimal()
            }
            sessionCount shouldBe 5
            validityInDays shouldBe null
            saleable shouldBe true
            traderBio.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "John"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
                active shouldBe true
            }
            description shouldBe "product-description"
            stripeIdentifier shouldBe "prod_1"
        }

        verify { stripeService.getPriceMap(listOf("prod_1")) }
    }

    @Test
    fun `should not search deleted products`() {
        every { stripeService.getPriceMap(listOf("prod_2")) } returns ProductPriceMap(
            listOf(
                ProductPrice(
                    "prod_2",
                    MoneyResult(200, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
            ),
        )

        dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
            dataHelper.getProduct(
                id = 1.toUUID(),
                traderId = trader.id,
                stripeIdentifier = "prod_1",
                entityModifier = { it.softDelete() },
            )
            dataHelper.getProduct(
                id = 2.toUUID(),
                traderId = trader.id,
                stripeIdentifier = "prod_2",
            )
        }

        val slice = underTest.handle(
            SearchProductQuery(
                infiniteScroll = InfiniteScrollAsc.Identifier(),
                filter = SearchProductQuery.Filter(searchString = null),
            ),
        )

        slice.content shouldHaveSize 1
        slice.hasMore shouldBe false
        slice.lastId shouldBe 2.toUUID()
        slice.content.single().productId shouldBe 2.toUUID()

        verify { stripeService.getPriceMap(listOf("prod_2")) }
    }

    @Test
    fun `should throw when product price is not found`() {
        every { stripeService.getPriceMap(listOf("prod_1")) } returns ProductPriceMap(
            listOf(),
        )

        val trader = dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader.id,
            stripeIdentifier = "prod_1",
        )

        val filter = SearchProductQuery.Filter(searchString = null)

        shouldThrow<ProductPriceNotFoundException> {
            underTest.handle(
                SearchProductQuery(
                    infiniteScroll = InfiniteScrollAsc.Identifier(),
                    filter = filter,
                ),
            )
        }

        verify { stripeService.getPriceMap(listOf("prod_1")) }
    }

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "null; [3,2,1]",
            "Product; [1]",
            "John; [2]",
            "Doe; [3]",
            "Prod; [2,1]",
            "Jo; [3,2]",
            "Do; [3,1]",
            "PRODUCT; [1]",
            "NonExistent; []",
            "John Doe; []",
        ],
    )
    fun `should search products by name`(
        searchString: String?,
        expectedIdsRaw: String,
    ) {
        // given
        val expectedIds: List<UUID> = expectedIdsRaw.parseIntegerList().map { it.toUUID() }

        every { stripeService.getPriceMap(any()) } returns ProductPriceMap(
            listOf(
                ProductPrice(
                    "prod_1",
                    MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
                ProductPrice(
                    "prod_2",
                    MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
                ProductPrice(
                    "prod_3",
                    MoneyResult(100, MonetaryCurrency.CZK, 21.toBigDecimal(), TaxBehaviour.INCLUSIVE),
                ),
            ),
        )

        val trader1 = dataHelper.getTrader(id = 1.toUUID(), firstName = "Adamdo", lastName = "Adamovic")
            .also { trader -> dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER) }
        val trader2 = dataHelper.getTrader(id = 2.toUUID(), firstName = "John", lastName = "Adamovic")
            .also { trader -> dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER) }
        val trader3 = dataHelper.getTrader(id = 3.toUUID(), firstName = "Josef", lastName = "Doe")
            .also { trader -> dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER) }

        dataHelper.getProduct(
            id = 1.toUUID(),
            traderId = trader1.id,
            name = "Product",
            stripeIdentifier = "prod_1",
        )
        dataHelper.getProduct(
            id = 2.toUUID(),
            traderId = trader2.id,
            name = "Another Prod",
            stripeIdentifier = "prod_2",
        )
        dataHelper.getProduct(
            id = 3.toUUID(),
            traderId = trader3.id,
            name = "Unknown",
            stripeIdentifier = "prod_3",
        )

        val slice = underTest.handle(
            SearchProductQuery(
                infiniteScroll = InfiniteScrollDesc.Identifier(),
                filter = SearchProductQuery.Filter(searchString = searchString),
            ),
        )

        // then
        slice.content.map { it.productId } shouldBe expectedIds

        val fetchedStripeProductIds = slice.content.map { it.stripeIdentifier }
        verify { stripeService.getPriceMap(fetchedStripeProductIds) }
    }
}
