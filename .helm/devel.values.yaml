#-----------------------
# Basic
#-----------------------
application:
  name: fundedmind-api-devel
  port: 8080

  resources:
    limits:
      memory: 1Gi
      cpu: 1
    requests:
      memory: 1Gi
      cpu: 500m

  readinessProbe:
    httpGet:
      port: 8282
      path: /actuator/health
    initialDelaySeconds: 60
    timeoutSeconds: 10
    periodSeconds: 15
    failureThreshold: 5

  env:
    - name: SPRING_PROFILES_ACTIVE
      value: devel
    - name: EMAIL_TEMPLATE_PASSWORD
      valueFrom:
        secretKeyRef:
          key: integration.email-template.password
          name: env-variables
    - name: DB_URL
      valueFrom:
        secretKeyRef:
          key: spring.datasource.url
          name: env-variables
    - name: DB_USERNAME
      valueFrom:
        secretKeyRef:
          key: spring.datasource.username
          name: env-variables
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: spring.datasource.password
          name: env-variables
    - name: SENTRY_DSN
      valueFrom:
        secretKeyRef:
          key: sentry.dsn
          name: env-variables
    - name: FIREBASE_CREDENTIALS
      valueFrom:
        secretKeyRef:
          key: firebase.credentials
          name: env-variables
    - name: SPRING_MAIL_HOST
      valueFrom:
        secretKeyRef:
          key: spring.mail.host
          name: env-variables
    - name: SPRING_MAIL_PORT
      valueFrom:
        secretKeyRef:
          key: spring.mail.port
          name: env-variables
    - name: SPRING_MAIL_USERNAME
      valueFrom:
        secretKeyRef:
          key: spring.mail.username
          name: env-variables
    - name: SPRING_MAIL_PASSWORD
      valueFrom:
        secretKeyRef:
          key: spring.mail.password
          name: env-variables
    - name: SPRING_MAIL_FROM
      valueFrom:
        secretKeyRef:
          key: spring.mail.properties.mail.smtp.from
          name: env-variables
    - name: STRIPE_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.stripe.api-key
          name: env-variables
    - name: STRIPE_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fundedmind.security.webhook.stripe.secret-key
          name: env-variables
    - name: HUBSPOT_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fundedmind.security.webhook.hubspot.secret-key
          name: env-variables
    - name: HUBSPOT_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.hubspot.api-key
          name: env-variables
    - name: DISCORD_CLIENT_APP_ACCESS_TOKEN
      valueFrom:
        secretKeyRef:
          key: integration.discord.client-app-access-token
          name: env-variables
    - name: CALENDLY_WEBHOOK_SECRET_KEY
      valueFrom:
        secretKeyRef:
          key: fundedmind.security.webhook.calendly.secret-key
          name: env-variables
    - name: CALENDLY_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.calendly.api-key
          name: env-variables
    - name: FUNDEDMIND_API_KEY
      valueFrom:
        secretKeyRef:
          key: fundedmind.security.api-key
          name: env-variables
    - name: FAKTUROID_CLIENT_ID
      valueFrom:
        secretKeyRef:
          key: integration.fakturoid.client-id
          name: env-variables
    - name: FAKTUROID_CLIENT_SECRET
      valueFrom:
        secretKeyRef:
          key: integration.fakturoid.client-secret
          name: env-variables
    - name: ZAPIER_API_KEY
      valueFrom:
        secretKeyRef:
          key: integration.zapier.api-key
          name: env-variables

  image:
    repository: gitlab.cleevio.cz:4567/backend/funded-mind-api/main
    tag: latest

  replicaCount: 1

#-----------------------
# Ingress
#-----------------------
ingress:
  certificateIssuer: letsencrypt-http

  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"

  hosts:
    - host: api.fundedmind.devel.cleevio.dev
      paths: [ "/" ]

  tls:
    - hosts:
        - api.fundedmind.devel.cleevio.dev

#-----------------------
# Service
#-----------------------
service:
  port: 8080

#-----------------------
# Persistent Volume
#-----------------------
persistentVolume:
  enabled: true                     # Enables / Disables mounting
  mountName: static-files           # (Optional) Mount name
  size: "5Gi"                       # Size of mount
  mountPath: "/upload"              # Mount path in the container
  storageClassName: "nfs"           # Storage class name - different on each provider
  cdn:
    enabled: true
    host: cdn.fundedmind.devel.cleevio.dev
