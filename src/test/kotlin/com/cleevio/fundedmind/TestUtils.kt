package com.cleevio.fundedmind

import com.cleevio.fundedmind.application.common.util.toAlphaNumeric
import com.cleevio.fundedmind.application.common.util.toUUID
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.util.ReflectionUtils
import java.io.InputStream
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.util.Optional
import java.util.UUID

// used to override private properties
fun <T : Any> T.setAndReturnPrivateProperty(
    variableName: String,
    data: Any?,
): Any? {
    val field = ReflectionUtils.findField(javaClass, variableName) ?: throw Exception("No such field $variableName")
    return field.let {
        it.isAccessible = true
        it.set(this, data)
        return@let it.get(this)
    }
}

fun <T : Any> T.setAndReturnPrivatePropertyInSuperClass(
    variableName: String,
    data: Any,
): Any? {
    var clazz = javaClass.superclass
    while (clazz != null) {
        val field = ReflectionUtils.findField(clazz, variableName)
        if (field != null) {
            return field.let {
                it.isAccessible = true
                it.set(this, data)
                return@let it.get(this)
            }
        }
        clazz = clazz.superclass
    }
    throw Exception("No such field $variableName")
}

fun <T : Any> T.toOptional(): Optional<T> = Optional.of(this)

infix fun Instant?.shouldBeAbout(expected: Instant?): Instant? {
    val truncatedThis = this?.truncatedTo(ChronoUnit.SECONDS)
    val truncatedExpected = expected?.truncatedTo(ChronoUnit.SECONDS)

    return truncatedThis shouldBe truncatedExpected
}

infix fun Instant?.truncatedShouldBe(expected: Instant?): Instant? =
    this?.roundToMicros() shouldBe expected?.roundToMicros()

infix fun <T : Comparable<T>> T?.shouldNotBeNullAndBeEqualComparingTo(other: T) =
    this.shouldNotBeNull() shouldBeEqualComparingTo (other)

private fun Instant.roundToMicros() = this.plusNanos(500).truncatedTo(ChronoUnit.MICROS)

fun inputStreamContentEquals(expectedContent: InputStream): (InputStream) -> Boolean = { actualInputStream ->
    actualInputStream.readBytes().contentEquals(expectedContent.readBytes())
}

fun String.toLocalDate(): LocalDate = LocalDate.parse(this)

fun Int.toUUID(): UUID {
    assert(this >= 0)
    return createUuidFromNumber(this.toLong())
}

private fun createUuidFromNumber(number: Long): UUID {
    val uuidString = String.format("00000000-000-0000-0000-%012d", number)
    return uuidString.replaceRange(9, 12, "000").toUUID()
}

fun String.parseIntegerList(): List<Int> = this.toString().parseList { it.toInt() }
fun String.parseStudentTierList(): List<StudentTier> = this.toString().parseList { StudentTier.valueOf(it) }

private fun <T> String.parseList(
    delimiter: Char = ',',
    transform: (String) -> T,
): List<T> = this
    .removeSurrounding("[", "]")
    .takeIf { it.isNotBlank() }
    ?.split(delimiter)
    ?.map(String::trim)
    ?.map(transform)
    ?: emptyList()

val UUID.suffix: String
    get() = this.toAlphaNumeric().takeLast(12)

fun String.toInstant(): Instant = Instant.parse(this)

fun String.toMockMultipartFile() = run {
    require(this.isNotBlank())

    MockMultipartFile(
        /* name = */
        this,
        /* originalFilename = */
        "$this.png",
        /* contentType = */
        MediaType.IMAGE_PNG_VALUE,
        /* content = */
        this.toByteArray(),
    )
}
