package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.AppButtonWithLinkInput
import com.cleevio.fundedmind.application.module.gamelevelreward.command.UpdateGameLevelRewardCommand
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import com.cleevio.fundedmind.domain.gamelevelreward.exception.GameLevelRewardButtonWithoutLinkException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.UUID

class UpdateGameLevelRewardCommandHandlerTest(
    @Autowired private val underTest: UpdateGameLevelRewardCommandHandler,
    @Autowired private val gameLevelRewardRepository: GameLevelRewardRepository,
) : IntegrationTest() {

    @Test
    fun `should update existing level reward`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())

        // when
        underTest.handle(
            defaultCommand(
                gameLevelRewardId = 1.toUUID(),
                name = "New Name",
                gameLevel = GameLevel.FIVE,
                type = GameLevelRewardType.PHYSICAL,
                description = "New description",
                rewardCouponCode = "NEW123",
                rewardButton = null,
            ),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!

        gameLevelReward.run {
            name shouldBe "New Name"
            gameLevel shouldBe GameLevel.FIVE
            type shouldBe GameLevelRewardType.PHYSICAL
            description shouldBe "New description"
            rewardCouponCode shouldBe "NEW123"
            rewardButton shouldBe null
        }
    }

    @Test
    fun `should update level reward with button and without coupon code`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())

        // when
        underTest.handle(
            defaultCommand(
                gameLevelRewardId = 1.toUUID(),
                rewardCouponCode = null,
                rewardButton = AppButtonWithLinkInput(
                    text = "Button",
                    color = Color.GREEN,
                    linkUrl = "https://example.com",
                ),
            ),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!

        gameLevelReward.run {
            rewardCouponCode shouldBe null
            rewardButton!!.run {
                text shouldBe "Button"
                color shouldBe Color.GREEN
                linkUrl shouldBe "https://example.com"
            }
        }
    }

    @Test
    fun `should remove description, coupon code, and button`() {
        // given
        dataHelper.getGameLevelReward(
            id = 1.toUUID(),
            description = "Description",
            rewardCouponCode = "CODE123",
            rewardButton = AppButtonWithLink("Button", Color.GREEN, "https://example.com"),
        )

        // when
        underTest.handle(
            defaultCommand(
                gameLevelRewardId = 1.toUUID(),
                description = null,
                rewardCouponCode = null,
                rewardButton = null,
            ),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!

        gameLevelReward.run {
            description shouldBe null
            rewardCouponCode shouldBe null
            rewardButton shouldBe null
        }
    }

    @Test
    fun `should throw when updating level reward with button with empty link`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())

        // when & then
        shouldThrow<GameLevelRewardButtonWithoutLinkException> {
            underTest.handle(
                defaultCommand(
                    gameLevelRewardId = 1.toUUID(),
                    rewardCouponCode = null,
                    rewardButton = AppButtonWithLinkInput(
                        text = "Button",
                        color = Color.BLUE,
                        linkUrl = "",
                    ),
                ),
            )
        }
    }

    private fun defaultCommand(
        gameLevelRewardId: UUID,
        name: String = "New Name",
        gameLevel: GameLevel = GameLevel.FIVE,
        type: GameLevelRewardType = GameLevelRewardType.PHYSICAL,
        description: String? = "New description",
        rewardCouponCode: String? = "NEW123",
        rewardButton: AppButtonWithLinkInput?,
    ) = UpdateGameLevelRewardCommand(
        gameLevelRewardId = gameLevelRewardId,
        name = name,
        gameLevel = gameLevel,
        type = type,
        description = description,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton,
    )
}
