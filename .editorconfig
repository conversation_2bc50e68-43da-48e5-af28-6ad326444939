[*]
insert_final_newline = true

[*.{kt,kts}]

ktlint_code_style = intellij_idea
ktlint_standard = enabled
ktlint_experimental = enabled
ktlint_custom-rule-set = disabled


ktlint_standard_package-name = disabled
ktlint_standard_filename = enabled
ktlint_standard_annotation = disabled
ktlint_standard_trailing-comma-on-call-site = enabled
ktlint_standard_trailing-comma = enabled
ktlint_standard_no-line-break-before-assignment = disabled
ktlint_function_signature_rule_force_multiline_when_parameter_count_greater_or_equal_than = 2
ktlint_standard_parameter-list-wrapping = enabled
ktlint_standard_class-signature = disabled

indent_style = space
tab_width = 4
ij_kotlin_indent_before_arrow_on_new_line = true
max_line_length = 120
ij_kotlin_line_break_after_multiline_when_entry = true

trim_trailing_whitespace = true
ij_kotlin_allow_trailing_comma_on_call_site = true
ij_kotlin_allow_trailing_comma = true
ij_kotlin_packages_to_use_import_on_demand = 9999
ij_kotlin_name_count_to_use_star_import = 9999
ij_kotlin_name_count_to_use_star_import_for_members = 9999
