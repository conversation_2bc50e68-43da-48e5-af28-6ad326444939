package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.highlight.query.ListHighlightsQuery
import com.cleevio.fundedmind.domain.common.AppButton
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.parseStudentTierList
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class ListHighlightsQueryHandlerTest(
    @Autowired private val underTest: ListHighlightsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list highlights - verify mappings`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getHighlight(
            id = 1.toUUID(),
            listingOrder = 1,
            title = "Highlight 1",
            description = "Description 1",
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            linkUrl = "highlight-url",
            button = AppButton("Button", Color.YELLOW),
            entityModifier = {
                it.changeImageDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.HIGHLIGHT_DESKTOP_PHOTO,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeImageMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.HIGHLIGHT_MOBILE_PHOTO,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            highlightId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            published shouldBe false
            imageDesktop shouldNotBe null
            imageDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            imageMobile shouldNotBe null
            imageMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            title shouldBe "Highlight 1"
            description shouldBe "Description 1"
            linkUrl shouldBe "highlight-url"
            button!!.run {
                text shouldBe "Button"
                color shouldBe Color.YELLOW
            }
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE)
            visibleToDiscordUsers shouldBe true
        }
    }

    @Test
    fun `admin should see all highlights`() {
        // given
        val admin = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getHighlight(id = 1.toUUID(), visibleToTiers = listOf(StudentTier.BASECAMP))
        dataHelper.getHighlight(id = 2.toUUID(), visibleToTiers = listOf(StudentTier.MASTERCLASS))
        dataHelper.getHighlight(id = 3.toUUID(), visibleToTiers = listOf(StudentTier.EXCLUSIVE))
        dataHelper.getHighlight(id = 4.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = true)
        dataHelper.getHighlight(id = 5.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = false)
        dataHelper.getHighlight(
            id = 6.toUUID(),
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = {
                it.changeImageDesktop(fileId = dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(fileId = dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
                it.publish()
            },
        )
        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = admin.id),
        )

        // then
        result.data.map { it.highlightId } shouldContainExactlyInAnyOrder setOf(
            1.toUUID(),
            2.toUUID(),
            3.toUUID(),
            4.toUUID(),
            5.toUUID(),
            6.toUUID(),
        )
    }

    @Test
    fun `trader should see only published highlights`() {
        // given
        val trader = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.TRADER)

        dataHelper.getHighlight(id = 1.toUUID(), visibleToTiers = listOf(StudentTier.BASECAMP))
        dataHelper.getHighlight(id = 2.toUUID(), visibleToTiers = listOf(StudentTier.MASTERCLASS))
        dataHelper.getHighlight(id = 3.toUUID(), visibleToTiers = listOf(StudentTier.EXCLUSIVE))
        dataHelper.getHighlight(id = 4.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = true)
        dataHelper.getHighlight(id = 5.toUUID(), visibleToTiers = listOf(), visibleToDiscordUsers = false)
        dataHelper.getHighlight(
            id = 6.toUUID(),
            visibleToTiers = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = {
                it.changeImageDesktop(fileId = dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(fileId = dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
                it.publish()
            },
        )
        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = trader.id),
        )

        // then
        result.data.map { it.highlightId } shouldContainExactlyInAnyOrder setOf(6.toUUID())
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "[BASECAMP], false",
            "[MASTERCLASS], true",
            "[EXCLUSIVE], true",
            "[ ], true",
        ],
    )
    fun `should return empty highlights if PUBLISHED highlight is not visible to student based on tier`(
        visibleTiers: String,
        shouldBeEmpty: Boolean?,
    ) {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getHighlight(
            id = 1.toUUID(),
            visibleToTiers = visibleTiers.parseStudentTierList(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data.isEmpty() shouldBe shouldBeEmpty
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "[BASECAMP], true",
            "[MASTERCLASS], true",
            "[EXCLUSIVE], true",
            "[ ], true",
        ],
    )
    fun `should return empty highlights to student if highlight is not PUBLISHED`(
        visibleTiers: String,
        shouldBeEmpty: Boolean?,
    ) {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP)

        dataHelper.getHighlight(
            id = 1.toUUID(),
            visibleToTiers = visibleTiers.parseStudentTierList(),
            entityModifier = { it.hide() },
        )

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data.isEmpty() shouldBe shouldBeEmpty
    }

    @Test
    fun `should list highlights - should sort by listingOrder ASC`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getHighlight(id = 1.toUUID(), listingOrder = 2)
        dataHelper.getHighlight(id = 2.toUUID(), listingOrder = 3)
        dataHelper.getHighlight(id = 3.toUUID(), listingOrder = 1)

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 3
        result.data.map { it.highlightId } shouldBe listOf(3.toUUID(), 1.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list highlights - should filter out deleted highlights`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        dataHelper.getHighlight(id = 1.toUUID())
        dataHelper.getHighlight(id = 2.toUUID(), entityModifier = { it.softDelete() })

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 1
        result.data.map { it.highlightId } shouldBe listOf(1.toUUID())
    }

    @Test
    fun `should show highlights to student with discord access when visibleToDiscordUsers is true`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(1, ChronoUnit.DAYS))
            },
        )

        dataHelper.getHighlight(
            id = 1.toUUID(),
            visibleToTiers = listOf(), // Empty list means no tiers are allowed
            visibleToDiscordUsers = true, // But Discord users are allowed
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().highlightId shouldBe 1.toUUID()
    }

    @Test
    fun `should not show highlights to student without discord access when only visibleToDiscordUsers is true`() {
        // given
        dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = 1.toUUID(),
            studentTier = StudentTier.BASECAMP,
            // No Discord subscription activated
            entityModifier = { it.deactivateDiscordSubscription() },
        )

        dataHelper.getHighlight(
            id = 1.toUUID(),
            visibleToTiers = listOf(), // Empty list means no tiers are allowed
            visibleToDiscordUsers = true, // But Discord users are allowed
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.HIGHLIGHT_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.HIGHLIGHT_MOBILE_PHOTO).id)
                it.publish()
            },
        )

        // when
        val result = underTest.handle(
            ListHighlightsQuery(userId = 1.toUUID()),
        )

        // then
        result.data shouldHaveSize 0
    }
}
