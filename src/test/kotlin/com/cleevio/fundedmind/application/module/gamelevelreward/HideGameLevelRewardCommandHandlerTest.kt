package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelreward.command.HideGameLevelRewardCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class HideGameLevelRewardCommandHandlerTest(
    @Autowired private val underTest: HideGameLevelRewardCommandHandler,
    @Autowired private val gameLevelRewardRepository: GameLevelRewardRepository,
) : IntegrationTest() {

    @Test
    fun `should hide level reward that was previously published`() {
        // given
        dataHelper.getGameLevelReward(
            id = 1.toUUID(),
            entityModifier = {
                it.changeRewardPhoto(dataHelper.getImage(type = FileType.GAME_LEVEL_REWARD_PHOTO).id)
                it.publish()
            },
        )
        gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!.published shouldBe true

        // when
        underTest.handle(HideGameLevelRewardCommand(gameLevelRewardId = 1.toUUID()))

        // then
        gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }

    @Test
    fun `hiding level reward that was not published should not throw`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())

        gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false

        // when
        underTest.handle(HideGameLevelRewardCommand(gameLevelRewardId = 1.toUUID()))

        // then
        gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }
}
