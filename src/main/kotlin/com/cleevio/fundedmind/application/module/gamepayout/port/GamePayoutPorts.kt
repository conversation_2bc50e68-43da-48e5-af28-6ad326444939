package com.cleevio.fundedmind.application.module.gamepayout.port

import com.cleevio.fundedmind.application.module.gamepayout.query.GamePayoutOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import java.math.BigDecimal
import java.time.Year

interface GamePayoutOverviewPort {
    fun getGamePayoutOverview(year: Year): PayoutOverview

    data class PayoutOverview(
        val offsetTotalPayout: BigDecimal,
        val realTotalPayout: BigDecimal,
        val currentPayoutGoal: BigDecimal,
        val approvedPayouts: Int,
        val waitingDocumentsCount: Int,
        val year: Int,
    )
}

interface GetLatestStudentPayoutsPort {
    fun getByYear(
        year: Year,
        limit: Int,
        gameLevelMapper: (GameLevel, LevelVisibility) -> GameLevel?,
    ): List<GamePayoutOverviewQuery.LatestStudentPayout>
}
