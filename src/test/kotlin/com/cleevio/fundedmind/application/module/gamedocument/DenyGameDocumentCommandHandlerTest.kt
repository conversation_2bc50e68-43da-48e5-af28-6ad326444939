package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.DenyGameDocumentCommand
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentHasWrongStateException
import com.cleevio.fundedmind.domain.gamelevelprogress.GameLevelProgressRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate

class DenyGameDocumentCommandHandlerTest(
    @Autowired private val underTest: DenyGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
    @Autowired private val studentRepository: StudentRepository,
    @Autowired private val gameLevelProgressRepository: GameLevelProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should deny game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.ONE,
            )
        }

        // Create a game document to deny
        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = BigDecimal("500.00"),
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.FOUR,
            payoutDate = LocalDate.now(),
            truthScore = 80,
            scoreMessage = "Achievement to deny",
        )

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)

        every { sendEmailService.sendEmailGameDocumentDenied(1.toUUID()) } just Runs

        // Deny the game document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = gameDocument.id,
                denyMessage = "Document does not meet our standards",
                now = "2025-09-15T10:00:00Z".toInstant(),
            ),
        )

        // Verify the denial
        gameDocumentRepository.findByIdOrNull(gameDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Document does not meet our standards"
            deniedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
        }

        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE)

        verify { sendEmailService.sendEmailGameDocumentDenied(1.toUUID()) }
    }

    @Test
    fun `should throw exception when game document is not in WAITING state`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create a game document that is already approved
        val gameDocument = dataHelper.getGameDocument(
            id = 304.toUUID(),
            studentId = user.id,
            entityModifier = { it.denyAwaiting("already denied") },
        )

        // Try to approve the already approved document
        shouldThrow<GameDocumentHasWrongStateException> {
            underTest.handle(
                DenyGameDocumentCommand(
                    gameDocumentId = gameDocument.id,
                    denyMessage = "Lol git gud",
                    now = "2025-09-15T10:00:00Z".toInstant(),
                ),
            )
        }
    }

    @Test
    fun `should revoke document and recompute awaiting documents`() {
        // Create a student with AppUser at level FIVE
        val user = dataHelper.getAppUser(id = 301.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FIVE,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FOUR)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FIVE)

        // Create an approved payout document that moved student to level FIVE
        val approvedDocument = dataHelper.getGameDocument(
            id = 601.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 6_000.toBigDecimal(),
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.FIVE,
            payoutDate = LocalDate.now(),
            truthScore = 85,
            scoreMessage = "Good achievement",
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        // Create a waiting document that would move student from level FIVE to SIX
        val waitingDocument = dataHelper.getGameDocument(
            id = 602.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FOR_TRADERS,
            payoutAmount = 2_000.toBigDecimal(),
            previousLevel = GameLevel.FIVE,
            reachedLevel = GameLevel.SIX,
            payoutDate = LocalDate.now(),
            truthScore = 90,
            scoreMessage = "Waiting for approval",
        )

        every { sendEmailService.sendEmailGameDocumentDenied(601.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved payout document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedDocument.id,
                denyMessage = "Document contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Document contains errors"
            deniedAt shouldBeAbout "2025-09-15T10:00:00Z".toInstant()
            approvedAt shouldBeAbout "2025-09-10T10:00:00Z".toInstant()
        }

        // Verify student level is recalculated to ONE (no other approved documents)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        // Verify the waiting document has been recomputed
        gameDocumentRepository.findByIdOrNull(waitingDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.WAITING
            payoutAmount!! shouldBeEqualComparingTo 2_000.toBigDecimal()
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.FOUR
        }

        // Verify progress entries for levels 3, 4, and 5 are deleted
        gameLevelProgressRepository.findAllByStudentId(user.id).shouldBeEmpty()

        verify {
            sendEmailService.sendEmailGameDocumentDenied(601.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.ONE)
        }
    }

    @Test
    fun `should revoke backtesting and demote student to level ONE when strategy course completed`() {
        // Create a student with AppUser at level TWO
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.TWO,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)

        dataHelper.getCourse(
            id = 3.toUUID(),
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
        ).also { course ->
            dataHelper.getCourseProgress(
                id = 4.toUUID(),
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-09-01T10:00:00Z".toInstant(),
            )
        }

        // Create an approved backtesting document that moved student to level TWO
        val approvedDocument = dataHelper.getGameDocument(
            id = 5.toUUID(),
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(5.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved backtesting document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedDocument.id,
                denyMessage = "Document contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedDocument.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Document contains errors"
        }

        // Verify student level is recalculated to ONE (since they have a strategy course but no backtesting)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        // Verify progress entry for level TWO is deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(5.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.ONE)
        }
    }

    @Test
    fun `should revoke certificate and revert to level TWO when seen strategy and backtesting approved`() {
        // Create a student with AppUser at level THREE
        val user = dataHelper.getAppUser(id = 601.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)

        // Create a strategy course and mark it as completed
        dataHelper.getCourse(
            id = 602.toUUID(),
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
        ).also { course ->
            dataHelper.getCourseProgress(
                id = 603.toUUID(),
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-09-01T10:00:00Z".toInstant(),
            )
        }

        // Create an approved backtesting document
        dataHelper.getGameDocument(
            id = 604.toUUID(),
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
            entityModifier = { it.approveAwaiting("2025-09-05T10:00:00Z".toInstant()) }, // 5.9.2025
        )

        // Create an approved certificate document that moved student to level THREE
        val approvedCertificate = dataHelper.getGameDocument(
            id = 605.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.THREE,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(605.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved certificate document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedCertificate.id,
                denyMessage = "Certificate contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedCertificate.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Certificate contains errors"
        }

        // Verify student level is recalculated to TWO (since they have a strategy course and backtesting)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.TWO

        // Verify progress entry for level THREE is deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE, GameLevel.TWO)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(605.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.TWO)
        }
    }

    @Test
    fun `should revoke certificate and revert to level ONE when backtesting approved but no strategy`() {
        // Create a student with AppUser at level THREE
        val user = dataHelper.getAppUser(id = 701.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)

        // Create a strategy course but don't mark it as completed
        dataHelper.getCourse(
            id = 702.toUUID(),
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
        )

        // Create an approved backtesting document
        dataHelper.getGameDocument(
            id = 704.toUUID(),
            studentId = user.id,
            type = GameDocumentType.BACKTESTING,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.TWO,
            entityModifier = { it.approveAwaiting("2025-09-05T10:00:00Z".toInstant()) }, // 5.9.2025
        )

        // Create an approved certificate document that moved student to level THREE
        val approvedCertificate = dataHelper.getGameDocument(
            id = 705.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.THREE,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(705.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved certificate document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedCertificate.id,
                denyMessage = "Certificate contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedCertificate.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Certificate contains errors"
        }

        // Verify student level is recalculated to ONE (since they have backtesting but no strategy course)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        // Verify progress entries for levels TWO and THREE are deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(705.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.ONE)
        }
    }

    @Test
    fun `should revoke certificate and revert to level ONE when seen strategy but no backtesting`() {
        // Create a student with AppUser at level THREE
        val user = dataHelper.getAppUser(id = 801.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)

        // Create a strategy course and mark it as completed
        dataHelper.getCourse(
            id = 802.toUUID(),
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
        ).also { course ->
            dataHelper.getCourseProgress(
                id = 803.toUUID(),
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-09-01T10:00:00Z".toInstant(),
            )
        }

        // Create an approved certificate document that moved student to level THREE
        val approvedCertificate = dataHelper.getGameDocument(
            id = 805.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.THREE,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(805.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved certificate document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedCertificate.id,
                denyMessage = "Certificate contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedCertificate.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Certificate contains errors"
        }

        // Verify student level is recalculated to ONE (since they have strategy course but no backtesting)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        // Verify progress entries for levels TWO and THREE are deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(805.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.ONE)
        }
    }

    @Test
    fun `should revoke certificate and revert to level ONE when no strategy course and no backtesting`() {
        // Create a student with AppUser at level THREE
        val user = dataHelper.getAppUser(id = 901.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)

        // Create an approved certificate document that moved student to level THREE
        val approvedCertificate = dataHelper.getGameDocument(
            id = 905.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.THREE,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(905.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved certificate document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedCertificate.id,
                denyMessage = "Certificate contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedCertificate.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Certificate contains errors"
        }

        // Verify student level is recalculated to ONE (since they have no strategy course and no backtesting)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        // Verify progress entries for levels TWO and THREE are deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(905.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.ONE)
        }
    }

    @Test
    fun `should revoke payout and revert to level THREE when certificate approved`() {
        // Create a student with AppUser at level SIX
        val user = dataHelper.getAppUser(id = 1001.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.SIX,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FOUR)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FIVE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.SIX)

        // Create an approved certificate document
        dataHelper.getGameDocument(
            id = 1005.toUUID(),
            studentId = user.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            previousLevel = GameLevel.TWO,
            reachedLevel = GameLevel.THREE,
            entityModifier = { it.approveAwaiting("2025-09-07T10:00:00Z".toInstant()) }, // 7.9.2025
        )

        // Create an approved payout document that moved student to level SIX
        val approvedPayout = dataHelper.getGameDocument(
            id = 1006.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 16_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.SIX,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(1006.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved payout document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedPayout.id,
                denyMessage = "Payout contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedPayout.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Payout contains errors"
        }

        // Verify student level is recalculated to THREE (based on the certificate document)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.THREE

        // Verify progress entries for levels FOUR, FIVE, and SIX are deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE, GameLevel.TWO, GameLevel.THREE)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(1006.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.THREE)
        }
    }

    @Test
    fun `should revoke payout and revert to level ONE when no strategy, no backtesting, no certificate`() {
        // Create a student with AppUser at level SIX
        val user = dataHelper.getAppUser(id = 1101.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.SIX,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FOUR)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FIVE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.SIX)

        // Create an approved payout document that moved student to level SIX
        val approvedPayout = dataHelper.getGameDocument(
            id = 1106.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 16_000.toBigDecimal(),
            previousLevel = GameLevel.ONE,
            reachedLevel = GameLevel.SIX,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(1106.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the approved payout document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedPayout.id,
                denyMessage = "Payout contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedPayout.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Payout contains errors"
        }

        // Verify student level is recalculated to ONE (since they have no other approved documents)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.ONE

        // Verify progress entries for levels TWO through SIX are deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.ONE)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(1106.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.ONE)
        }
    }

    @Test
    fun `should revoke payout and revert to level FOUR when another payout exists`() {
        // Create a student with AppUser at level SIX
        val user = dataHelper.getAppUser(id = 1201.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.SIX,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.TWO)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.THREE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FOUR)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FIVE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.SIX)

        // Create an approved payout document of 11k
        val approvedPayout1 = dataHelper.getGameDocument(
            id = 1202.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 11_000.toBigDecimal(),
            previousLevel = GameLevel.FOUR,
            reachedLevel = GameLevel.SIX,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        // Create another approved payout document of 4k
        dataHelper.getGameDocument(
            id = 1203.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 4_000.toBigDecimal(),
            previousLevel = GameLevel.FOUR,
            reachedLevel = GameLevel.FIVE,
            entityModifier = { it.approveAwaiting("2025-09-05T10:00:00Z".toInstant()) }, // 5.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(1202.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the 11k payout document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = approvedPayout1.id,
                denyMessage = "Payout contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(approvedPayout1.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Payout contains errors"
        }

        // Verify student level is recalculated to FOUR (based on the remaining 4k payout)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.FOUR

        // Verify progress entries for levels 5 and 6 are deleted
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder
            listOf(GameLevel.ONE, GameLevel.TWO, GameLevel.THREE, GameLevel.FOUR)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(1202.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.FOUR)
        }
    }

    @Test
    fun `should revoke small payout and remain at level SIX when larger payout exists`() {
        // Create a student with AppUser at level SIX
        val user = dataHelper.getAppUser(id = 1301.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.SIX,
            )
        }

        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.FIVE)
        dataHelper.getGameLevelProgress(studentId = user.id, gameLevel = GameLevel.SIX)

        // Create an approved payout document of 17k
        dataHelper.getGameDocument(
            id = 1302.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 17_000.toBigDecimal(),
            previousLevel = GameLevel.THREE,
            reachedLevel = GameLevel.SIX,
            entityModifier = { it.approveAwaiting("2025-09-05T10:00:00Z".toInstant()) }, // 5.9.2025
        )

        // Create another approved payout document of 2k
        val smallPayout = dataHelper.getGameDocument(
            id = 1303.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 2_000.toBigDecimal(),
            previousLevel = GameLevel.SIX,
            reachedLevel = GameLevel.SIX,
            entityModifier = { it.approveAwaiting("2025-09-10T10:00:00Z".toInstant()) }, // 10.9.2025
        )

        every { sendEmailService.sendEmailGameDocumentDenied(1303.toUUID()) } just Runs
        every { hubspotService.updateCrmUserGameLevel(any(), any()) } just Runs

        // Deny the 2k payout document
        underTest.handle(
            DenyGameDocumentCommand(
                gameDocumentId = smallPayout.id,
                denyMessage = "Payout contains errors",
                now = "2025-09-15T10:00:00Z".toInstant(), // 15.9.2025
            ),
        )

        // Verify the document is now denied
        gameDocumentRepository.findByIdOrNull(smallPayout.id)!!.run {
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Payout contains errors"
        }

        // Verify student level remains at SIX
        // (based on the remaining 17k payout which is still above the 15k threshold for level 6)
        studentRepository.findByIdOrNull(user.id)!!.gameLevel shouldBe GameLevel.SIX

        // Verify progress entries for levels 5 and 6 are NOT deleted (since level didn't change)
        gameLevelProgressRepository
            .findAllByStudentId(user.id)
            .map { it.gameLevel } shouldContainExactlyInAnyOrder listOf(GameLevel.FIVE, GameLevel.SIX)

        verify {
            sendEmailService.sendEmailGameDocumentDenied(1303.toUUID())
            hubspotService.updateCrmUserGameLevel(user.hubspotIdentifier, GameLevel.SIX)
        }
    }
}
